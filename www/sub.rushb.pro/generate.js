function toggleInput(id) {
    var checkbox = document.getElementById(id + '-checkbox');
    var inputContainer = document.getElementById(id + '-input-container');
    if (checkbox.checked) {
        inputContainer.style.display = 'block';
    } else {
        inputContainer.style.display = 'none';
    }
}

function generateOutput() {
    // 检查是否输入了链接
    if (!link.value) {
        alert("请输入链接");
        return;
    }
    if (!api.value) {
        api.value = "sub.rushb.pro";
    }
    // 获取客户端
    var client = document.getElementById('client-select');
    // 是否为 Meta 内核
    let expand = {value: false};
    let target = {};
    let config = {value: "https://raw.githubusercontent.com/FanxJK/Rules/main/Clash.ini"};
    link.value = link.value.replace(/\r?\n/g, '|');
    
    if (client.value == "clash") {
        expand = {value: true};
        config.value = "https://raw.githubusercontent.com/FanxJK/Rules/main/Clash_old.ini";
    }
    if (client.value == "meta") {
        target.value = "clash";
    } else {
        target.value = client.value;
    }
    out.value = `https://${api.value
        }/sub?target=${encodeURIComponent(
            target.value
        )}&new_name=true&filename=${encodeURIComponent(
            filename.value
        )}&url=${encodeURIComponent(
            link.value
        )}&include=${encodeURIComponent(
            include.value
        )}&exclude=${encodeURIComponent(
            exclude.value
        )}&expand=${
            expand.value
        }&insert=true&emoji=true&list=false&tfo=true&udp=true&config=${
            config.value
        }`;

    const containerEl = document.querySelector(".container");
    if (!containerEl.querySelector(".copy-button")) {
        // 创建并添加复制按钮
        const copyButton = document.createElement("button");
        const copyIcon = document.createElement("i");
        copyIcon.className = "fas fa-copy copy-icon";
        copyButton.appendChild(copyIcon);
        copyButton.appendChild(document.createTextNode("复制到剪贴板"));
        copyButton.className = "button copy-button";
        copyButton.onclick = copyOutput;
        containerEl.appendChild(copyButton);
    }
}

function copyOutput() {
    const copyText = document.getElementById("out");
    navigator.clipboard.writeText(copyText.value);
    alert("已复制到剪贴板");
}
