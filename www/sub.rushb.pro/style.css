body {
  font-family: Arial, sans-serif;
  color: #333;
  background-color: #f2f2f2;
}

h1 {
  text-align: center;
  font-size: 50px;
  margin-top: 50px;
  color: #333;
  text-shadow: 2px 2px #e6e6e6;
}

h2 {
  text-align: center;
  font-size: 15px;
  margin-top: 10px;
  color: #333;
  text-shadow: 2px 2px #e6e6e6;
}

.container {
  width: 80%;
  margin: 0 auto;
  border-radius: 10px;
  padding: 20px;
  background-color: #fff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  margin-top: 50px;
}

.input {
  width: 100%;
  height: 60px;
  border-radius: 5px;
  border: 1px solid #ccc;
  margin-bottom: 20px;
  font-size: 16px;
  padding: 10px;
  color: #333;
  box-sizing: border-box;
}

.textarea {
  width: 100%;
  height: 200px;
  border-radius: 5px;
  border: 1px solid #ccc;
  margin-bottom: 20px;
  font-size: 16px;
  padding: 10px;
  color: #333;
  box-sizing: border-box;
}

.button {
  width: 100%;
  height: 40px;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  color: #fff;
  background-color: #333;
  cursor: pointer;
  margin-top: 20px;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-weight: bold;
}

.button:hover {
  background-color: #555;
}

.copy-button {
  margin-top: 10px;
  margin-right: 10px;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  color: #fff;
  background-color: #333;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-weight: bold;
}

.copy-button:hover {
  background-color: #555;
}

.copy-icon,
.clash-icon {
  margin-right: 10px;
}

/* Checkbox Styling */
.checkbox-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.checkbox-container input[type="checkbox"] {
  display: none;
}

.checkbox-container label {
  position: relative;
  padding-left: 30px;
  cursor: pointer;
}

.checkbox-container label::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 20px;
  height: 20px;
  border-radius: 5px;
  border: 1px solid #ccc;
  background-color: #fff;
}

.checkbox-container input[type="checkbox"]:checked+label::before {
  background-color: #333;
}

.checkbox-container label::after {
  content: "";
  position: absolute;
  left: 8px;
  top: 4px;
  width: 5px;
  height: 10px;
  border: 2px solid #fff;
  border-top: none;
  border-left: none;
  transform: rotate(45deg);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.checkbox-container input[type="checkbox"]:checked+label::after {
  opacity: 1;
}

/* Style for the kernel select dropdown */
.select-container {
  display: block; /* or 'flex' if you want to align it with a label on the same line */
  margin-bottom: 20px; /* Same as the .input margin-bottom */
}

.select-container label {
  display: block;
  margin-bottom: 5px;
  font-size: 16px;
  color: #333;
}

#client-select {
  width: 100%; /* Full width - same as .input */
  height: 60px; /* Same height as .input */
  border-radius: 5px; /* Same border radius as .input */
  border: 1px solid #ccc; /* Same border style as .input */
  font-size: 16px; /* Same font-size as .input */
  padding: 10px; /* Same padding as .input */
  color: #333; /* Same text color as .input */
  background-color: #fff; /* Same background color as .input */
  box-sizing: border-box; /* Same box-sizing as .input */
  appearance: none; /* Removes default styling provided by the browser */
  -moz-appearance: none; /* Firefox */
  -webkit-appearance: none; /* Safari and Chrome */
  cursor: pointer; /* Cursor pointer, consistent with button style */
}

#client-select:hover {
  border-color: #999; /* Darker border on hover */
}

#client-select:focus {
  border-color: #333; /* Darkest border when focused */
  outline: none; /* Removes the default focus outline */
}


#client-select {
  -webkit-appearance: none; /* 针对 Webkit 浏览器 */
  -moz-appearance: none;    /* 针对 Firefox */
  appearance: none;         /* 标准语法 */
  background-color: white; /* 按需应用颜色 */
  border: 1px solid #ccc; /* 按需应用边框 */
  
  /* 利用 CSS 创建下拉箭头 */
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="%23333" viewBox="0 0 140 140"><polygon points="70 90 40 50 100 50"/></svg>');
  background-position: right 10px center; /* 箭头定位 */
  /* background-size: 12px 12px;*/ /*箭头大小 */
  background-repeat: no-repeat; /* 不重复背景图 */

  padding-right: 30px; /* 确保箭头右侧有足够的间隔 */
  cursor: pointer; /* 将鼠标指针变为手指形状以指示可以点击 */
}

/* Media query for smaller screens */
@media (max-width: 768px) {
  #client-select {
    /* Adjustments for select element on smaller screens */
    height: 40px; /* Smaller height */
    padding: 5px; /* Smaller padding */
    font-size: 14px; /* Smaller font-size */
  }
}
