<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <title>sub</title>
  <link rel="stylesheet" href="style.css">
  <script src="generate.js" type="text/javascript"></script>
</head>

<body>
  <h1>能用就行</h1>
  <h2>旧内核已停止更新，强烈建议使用搭载 Clash Meta (Mihomo) 内核的客户端：<br/><a href="https://wiki.metacubex.one/startup/client/"
    target="_blank" rel="noopener noreferrer">https://wiki.metacubex.one/startup/client/</a></h2>
  <h2 style="color: #FF5733; font-weight: bold;">Clash 旧内核、Stash 请勿使用 Meta (Mihomo) 订阅链接，否则将无法正常启动</h2>
  <h2>部分提供商封禁第三方订阅转换器，请使用官方订阅或生成订阅链接后将本站域名替换为提供商的订阅转换器 API 域名</h2>
  <div class="container">
    <input type="text" class="input" autocomplete="off" id="filename" placeholder="文件名 (可选)">
    <textarea type="text" class="input" autocomplete="off" id="link" placeholder="链接(多条链接请用换行或 “｜“ 分隔)"></textarea>
    <input type="text" class="input" autocomplete="off" id="api" placeholder="API 域名 默认为本站 (可选)">
    <div class="select-container">
      <label for="client-select">选择客户端:</label>
      <select id="client-select">
        <option value="meta">Clash Meta (Mihomo)</option>
        <option value="clash">Clash / Stash</option>
        <option value="surfboard">Surfboard</option>
        <option value="singbox">sing-box</option>
      </select>
    </div>
    <div class="checkbox-container">
      <input type="checkbox" id="include-checkbox" onchange="toggleInput('include')">
      <label for="include-checkbox">保留匹配到的节点</label>
    </div>
    <div id="include-input-container" style="display: none;">
      <input type="text" class="input" autocomplete="off" id="include" placeholder="支持正则匹配 (示例: HK|JP)">
    </div>
    <div class="checkbox-container">
      <input type="checkbox" id="exclude-checkbox" onchange="toggleInput('exclude')">
      <label for="exclude-checkbox">排除匹配到的节点</label>
    </div>
    <div id="exclude-input-container" style="display: none;">
      <input type="text" class="input" autocomplete="off" id="exclude" placeholder="支持正则匹配 (示例: HK|JP)">
    </div>
    <textarea class="textarea" id="out" readonly></textarea>
    <button class="button" onclick="generateOutput()">生成</button>
  </div>
  <div style="text-align: center; margin-top: 20px;">
    <p>本网站由 <img src="cloudflare-icon.png" alt="Cloudflare Logo" width="30"><a href="https://www.cloudflare.com/"
        target="_blank" rel="noopener noreferrer">Cloudflare®</a> 加速</p>
    <p>Cloudflare、Docker、Nginx 对于本站的日志均 <strong>完全关闭或重定向到 /dev/null</strong> 以保证您的隐私安全</p>
  </div>
</body>

</html>