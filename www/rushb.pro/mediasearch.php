<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>能用就行</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.2/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <style>
        .progress {
            height: 25px;
            border-radius: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .progress-bar {
            border-radius: 15px;
            background: linear-gradient(45deg, #007bff, #0056b3);
            transition: width 0.3s ease;
        }

        #progress-text {
            display: block;
            margin-top: 8px;
            font-size: 0.9em;
            color: #6c757d;
        }

        .alert {
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .alert-danger {
            border-left: 4px solid #dc3545;
        }

        .alert-warning {
            border-left: 4px solid #ffc107;
        }

        .alert-info {
            border-left: 4px solid #17a2b8;
        }

        .list-group-item {
            border-radius: 8px !important;
            margin-bottom: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            transition: all 0.2s ease;
        }

        .list-group-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        #search-button {
            border-radius: 25px;
            padding: 8px 20px;
            transition: all 0.2s ease;
        }

        #search-button:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,123,255,0.3);
        }

        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }

        .form-control {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            transition: border-color 0.2s ease;
        }

        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }
    </style>
</head>
<body>
<div class="container my-5">
    <h1>能用就行</h1>
    <form class="form-inline my-4" method="POST" id="search-form">
        <div class="form-group mb-2">
            <input type="text" class="form-control" name="q" placeholder="请输入搜索关键字" value="<?php echo isset($_POST['q']) ? htmlspecialchars($_POST['q']) : ''; ?>">
        </div>
        <button type="submit" class="btn btn-primary mb-2 mx-2" id="search-button">
            <span id="search-text">搜索</span>
            <span id="search-spinner" class="spinner-border spinner-border-sm ml-2" role="status" aria-hidden="true" style="display: none;"></span>
        </button>
    </form>

    <!-- 进度条 -->
    <div id="progress-container" style="display: none;" class="mb-3">
        <div class="progress">
            <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
        </div>
        <small id="progress-text" class="text-muted">准备搜索...</small>
    </div>

    <!-- 错误显示区域 -->
    <div id="error-container" style="display: none;">
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <strong>错误！</strong> <span id="error-message"></span>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    </div>

    <div id="search-results">

<?php
ini_set('display_errors', 'on');
if (isset($_POST['q'])) {
    $query = $_POST['q'];
    $query = urlencode($query);

// 构造完整的URL
$url = "https://www.yppan.com/?s={$query}";

// 添加User-Agent头部
$headers = [
    "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.63",
    "referer: https://www.yppan.com/",
];

// 发送GET请求并获取响应内容
$content = file_get_contents($url, false, stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => implode("\r\n", $headers),
    ]
]));

// 解析HTML文档
$dom = new DOMDocument();
libxml_use_internal_errors(true);
$dom->loadHTML($content);
libxml_use_internal_errors(false);

// 提取所有class为"post"的a标签的href链接并访问
$results = [];
foreach ($dom->getElementsByTagName('div') as $div) {
    if ($div->getAttribute('class') === 'post') {
        $a = $div->getElementsByTagName('a')->item(0);
        if ($a !== null) {
            $link = $a->getAttribute('href');
            $content = file_get_contents($link, false, stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'header' => implode("\r\n", $headers),
                ]
            ]));

            // 解析HTML文档
            $dom2 = new DOMDocument();
            libxml_use_internal_errors(true);
            $content = preg_replace('/<amp-auto-ads[^>]*>.*?<\/amp-auto-ads>/is', '', $content); // 移除amp广告
            $dom2->loadHTML($content);
            libxml_use_internal_errors(false);

            // 提取包含资源信息的 p 元素
            $xpath = new DOMXPath($dom2);
            $resource_name = '';
            foreach ($xpath->query('//div[@class="entry"]/p') as $p) {
                if (preg_match('/(名称|名字|标题)：(.+)/', $p->nodeValue, $matches)) {
                    $resource_name = trim($matches[2]);
                    break;
                }
            }

            // 提取资源链接
            $entries = $xpath->query('//div[@class="entry"]/p');
            foreach ($entries as $entry) {
                $matches = [];
                if (preg_match_all('/https:\/\/(www|pan)\.(aliyundrive|alipan|quark)\.(com|cn)\/s\/\w+/', $entry->textContent, $matches)) {
                    foreach ($matches[0] as $link) {
                        $results[] = ['name' => $resource_name, 'link' => $link];
                    }
                }
            }
        }
    }
}

if (!empty($results)) {
    echo '<div class="list-group">';
    foreach ($results as $result) {
        echo '<a href="' . $result['link'] . '" target="_blank" class="list-group-item list-group-item-action">';
        echo '<div class="d-flex w-100 justify-content-between">';
        echo '<h5 class="mb-1">' . (empty($result['name']) ? '未知名称' : htmlspecialchars($result['name'])) . '</h5>';
        echo '<small>' . $result['link'] . '</small>';
        echo '</div>';
        echo '</a>';
    }
    echo '</div>';
} else if (isset($_POST['q'])) {
    echo '<div class="alert alert-warning" role="alert">抱歉，没有找到与您的搜索关键字匹配的结果。</div>';
}
}
?>
</div>
</div>

<script>
$(document).ready(function() {
    // 显示错误信息的函数
    function showError(message) {
        $('#error-message').text(message);
        $('#error-container').show();
        $('#progress-container').hide();
    }

    // 隐藏错误信息的函数
    function hideError() {
        $('#error-container').hide();
    }

    // 更新进度条的函数
    function updateProgress(percentage, text) {
        $('#progress-bar').css('width', percentage + '%').attr('aria-valuenow', percentage);
        $('#progress-text').text(text);
    }

    // 显示进度条的函数
    function showProgress() {
        $('#progress-container').show();
        updateProgress(0, '准备搜索...');
    }

    // 隐藏进度条的函数
    function hideProgress() {
        $('#progress-container').hide();
    }

    // 重置搜索按钮状态
    function resetSearchButton() {
        $('#search-button').attr('disabled', false);
        $('#search-text').text('搜索');
        $('#search-spinner').hide();
    }

    // 设置搜索按钮为加载状态
    function setSearchButtonLoading() {
        $('#search-button').attr('disabled', true);
        $('#search-text').text('搜索中');
        $('#search-spinner').show();
    }

    $('#search-form').submit(function(event) {
        event.preventDefault();
        var searchInput = $('[name="q"]');
        var searchValue = searchInput.val();

        // 隐藏之前的错误信息
        hideError();

        if (searchValue.trim() === "") {
            showError('请输入搜索关键字。');
            return;
        }

        // 开始搜索
        setSearchButtonLoading();
        showProgress();
        $('#search-results').empty();

        // 模拟进度更新
        var progressSteps = [
            { percent: 20, text: '正在连接搜索服务器...' },
            { percent: 40, text: '正在搜索资源...' },
            { percent: 60, text: '正在解析搜索结果...' },
            { percent: 80, text: '正在获取资源链接...' },
            { percent: 95, text: '正在整理结果...' }
        ];

        var currentStep = 0;
        var progressInterval = setInterval(function() {
            if (currentStep < progressSteps.length) {
                updateProgress(progressSteps[currentStep].percent, progressSteps[currentStep].text);
                currentStep++;
            }
        }, 800);

        $.ajax({
            type: 'POST',
            url: '',
            data: $(this).serialize(),
            timeout: 30000, // 30秒超时
            success: function(response) {
                clearInterval(progressInterval);
                updateProgress(100, '搜索完成！');

                setTimeout(function() {
                    hideProgress();
                    resetSearchButton();

                    try {
                        var searchResults = $(response).find('#search-results').html();
                        if (searchResults && searchResults.trim() !== '') {
                            $('#search-results').html(searchResults);
                        } else {
                            $('#search-results').html('<div class="alert alert-warning" role="alert">没有找到相关结果，请尝试其他关键字。</div>');
                        }
                    } catch (e) {
                        showError('解析搜索结果时出现错误，请重试。');
                        console.error('解析错误:', e);
                    }
                }, 500);
            },
            error: function(xhr, status, error) {
                clearInterval(progressInterval);
                hideProgress();
                resetSearchButton();

                var errorMessage = '搜索失败，请重试。';
                if (status === 'timeout') {
                    errorMessage = '搜索超时，请检查网络连接后重试。';
                } else if (status === 'error') {
                    errorMessage = '网络错误，请检查网络连接。';
                } else if (xhr.status === 404) {
                    errorMessage = '搜索服务暂时不可用。';
                } else if (xhr.status === 500) {
                    errorMessage = '服务器内部错误，请稍后重试。';
                }

                showError(errorMessage);
                console.error('AJAX错误:', status, error, xhr);
            }
        });
    });

    // 点击关闭错误提示时的处理
    $(document).on('click', '.alert .close', function() {
        $(this).closest('.alert').parent().hide();
    });
});
</script>

</body>
</html>
