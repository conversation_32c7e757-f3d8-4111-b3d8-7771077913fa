<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>能用就行</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.6.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <style>
        .progress {
            height: 25px;
            border-radius: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .progress-bar {
            border-radius: 15px;
            background: linear-gradient(45deg, #007bff, #0056b3);
            transition: width 0.3s ease;
        }

        #progress-text {
            display: block;
            margin-top: 8px;
            font-size: 0.9em;
            color: #6c757d;
        }

        .alert {
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .alert-danger {
            border-left: 4px solid #dc3545;
        }

        .alert-warning {
            border-left: 4px solid #ffc107;
        }

        .alert-info {
            border-left: 4px solid #17a2b8;
        }

        .list-group-item {
            border-radius: 8px !important;
            margin-bottom: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            transition: all 0.2s ease;
        }

        .list-group-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        #search-button {
            border-radius: 25px;
            padding: 8px 20px;
            transition: all 0.2s ease;
        }

        #search-button:hover:not(:disabled) {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,123,255,0.3);
        }

        .spinner-border-sm {
            width: 1rem;
            height: 1rem;
        }

        .form-control {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            transition: border-color 0.2s ease;
        }

        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
        }

        .card {
            border: none;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
        }

        .card-title {
            color: #2c3e50;
            font-weight: 600;
            line-height: 1.3;
        }

        .card-text {
            color: #6c757d !important;
        }

        .badge {
            font-size: 0.8em;
            padding: 0.5em 0.8em;
        }

        .badge i {
            margin-right: 0.3em;
        }

        .btn-group .btn {
            border-radius: 20px;
        }

        .btn-group .btn:first-child {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        .btn-group .btn:last-child {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }

        .btn i {
            margin-right: 0.4em;
        }
    </style>
</head>
<body>
<div class="container my-5">
    <h1>能用就行</h1>
    <form class="form-inline my-4" method="POST" id="search-form">
        <div class="form-group mb-2">
            <input type="text" class="form-control" name="q" placeholder="请输入搜索关键字" value="<?php echo isset($_POST['q']) ? htmlspecialchars($_POST['q']) : ''; ?>">
        </div>
        <button type="submit" class="btn btn-primary mb-2 mx-2" id="search-button">
            <span id="search-text">搜索</span>
            <span id="search-spinner" class="spinner-border spinner-border-sm ml-2" role="status" aria-hidden="true" style="display: none;"></span>
        </button>
    </form>

    <!-- 进度条 -->
    <div id="progress-container" style="display: none;" class="mb-3">
        <div class="progress">
            <div id="progress-bar" class="progress-bar progress-bar-striped" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
        <small id="progress-text" class="text-muted">准备搜索...</small>
    </div>

    <!-- 错误显示区域 -->
    <div id="error-container" style="display: none;">
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <strong>错误！</strong> <span id="error-message"></span>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    </div>

    <div id="search-results">

<?php
ini_set('display_errors', 'on');
if (isset($_POST['q'])) {
    $query = $_POST['q'];
    $query = urlencode($query);

// 构造完整的URL
$url = "https://www.yppan.com/?s={$query}";

// 添加User-Agent头部
$headers = [
    "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.63",
    "referer: https://www.yppan.com/",
];

// 发送GET请求并获取响应内容
$content = file_get_contents($url, false, stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => implode("\r\n", $headers),
    ]
]));

// 解析HTML文档
$dom = new DOMDocument();
libxml_use_internal_errors(true);
$dom->loadHTML($content);
libxml_use_internal_errors(false);

// 提取搜索结果链接 - 修正的解析逻辑
$results = [];
$xpath = new DOMXPath($dom);

// 查找所有包含 archives 链接的 a 标签
$links = $xpath->query('//a[contains(@href, "/archives/")]');
$total_links = $links->length;
$processed = 0;

if ($total_links === 0) {
    echo '<div class="alert alert-warning" role="alert">没有找到相关结果，请尝试其他关键字。</div>';
} else {
    // 限制处理的链接数量，避免超时
    $max_links = min($total_links, 10);

    foreach ($links as $link) {
        if ($processed >= $max_links) break;

        $href = $link->getAttribute('href');
        if (strpos($href, '/archives/') !== false && !strpos($href, '#')) {
            // 确保是完整的URL
            if (strpos($href, 'http') !== 0) {
                $href = 'https://www.yppan.com' . $href;
            }

            // 获取详情页面内容
            $detail_content = file_get_contents($href, false, stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'header' => implode("\r\n", $headers),
                ]
            ]));

            if ($detail_content !== false) {
                // 解析详情页面
                $detail_dom = new DOMDocument();
                libxml_use_internal_errors(true);
                $detail_content = preg_replace('/<amp-auto-ads[^>]*>.*?<\/amp-auto-ads>/is', '', $detail_content);
                $detail_dom->loadHTML($detail_content);
                libxml_use_internal_errors(false);

                $detail_xpath = new DOMXPath($detail_dom);

                // 提取页面内容
                $all_text = $detail_dom->textContent;

                // 提取资源名称 - 从页面内容中提取，而不是标题
                $resource_name = '';
                if (preg_match('/名称：([^\n\r]+)/', $all_text, $name_matches)) {
                    $resource_name = trim($name_matches[1]);
                } else {
                    // 如果没有找到"名称："，尝试从标题提取
                    $title_nodes = $detail_xpath->query('//h1 | //title');
                    if ($title_nodes->length > 0) {
                        $title_text = $title_nodes->item(0)->textContent;
                        // 清理标题，移除网站名称和多余内容
                        $resource_name = preg_replace('/\s*–\s*云盘盘.*$/', '', $title_text);
                        $resource_name = preg_replace('/^名称：/', '', $resource_name);
                        // 只取第一行作为标题
                        $lines = explode("\n", $resource_name);
                        $resource_name = trim($lines[0]);
                    }
                }

                // 提取描述
                $description = '';
                if (preg_match('/描述：([^链接]+?)(?=链接：|$)/s', $all_text, $desc_matches)) {
                    $description = trim($desc_matches[1]);
                    // 清理描述中的多余空白和换行
                    $description = preg_replace('/\s+/', ' ', $description);
                    // 限制描述长度
                    if (mb_strlen($description, 'UTF-8') > 150) {
                        $description = mb_substr($description, 0, 150, 'UTF-8') . '...';
                    }
                }

                // 提取大小信息
                $size = '';
                if (preg_match('/📁\s*大小：([^🏷]+)/', $all_text, $size_matches)) {
                    $size = trim($size_matches[1]);
                } elseif (preg_match('/大小：([^\n\r]+)/', $all_text, $size_matches)) {
                    $size = trim($size_matches[1]);
                }

                // 提取标签
                $tags = '';
                if (preg_match('/🏷\s*标签：([^\n\r]+)/', $all_text, $tag_matches)) {
                    $tags = trim($tag_matches[1]);
                }

                // 提取资源链接 - 查找所有云盘链接
                $matches = [];
                if (preg_match_all('/https:\/\/(www\.|pan\.)?(aliyundrive|alipan|quark)\.(com|cn)\/s\/[a-zA-Z0-9]+/', $all_text, $matches)) {
                    foreach ($matches[0] as $found_link) {
                        $results[] = [
                            'name' => $resource_name ?: '未知名称',
                            'link' => $found_link,
                            'description' => $description,
                            'size' => $size,
                            'tags' => $tags,
                            'source_url' => $href
                        ];
                    }
                }
            }

            $processed++;

            // 避免请求过快被封
            usleep(200000); // 0.2秒延迟
        }
    }
}

if (!empty($results)) {
    echo '<div class="alert alert-success" role="alert"><i class="fas fa-check-circle"></i> 找到 ' . count($results) . ' 个资源链接</div>';

    foreach ($results as $result) {
        // 检测云盘类型
        $cloud_type = '未知云盘';
        $cloud_icon = 'fas fa-cloud';
        $cloud_color = 'secondary';

        if (strpos($result['link'], 'quark') !== false) {
            $cloud_type = '夸克网盘';
            $cloud_icon = 'fas fa-bolt';
            $cloud_color = 'warning';
        } elseif (strpos($result['link'], 'ali') !== false) {
            $cloud_type = '阿里云盘';
            $cloud_icon = 'fas fa-cloud-download-alt';
            $cloud_color = 'info';
        }

        echo '<div class="card mb-3 shadow-sm">';
        echo '<div class="card-body">';

        // 标题
        echo '<h5 class="card-title mb-3">' . htmlspecialchars($result['name']) . '</h5>';

        // 大小信息
        if (!empty($result['size'])) {
            echo '<div class="mb-3">';
            echo '<span class="badge badge-light"><i class="fas fa-hdd"></i> ' . htmlspecialchars($result['size']) . '</span>';
            echo '</div>';
        }

        // 按钮
        echo '<a href="' . $result['link'] . '" target="_blank" class="btn btn-primary btn-sm">';
        echo '<i class="' . $cloud_icon . '"></i> 打开' . $cloud_type . '</a>';

        echo '</div>';
        echo '</div>';
    }
} else {
    echo '<div class="alert alert-warning" role="alert">没有找到相关结果，请尝试其他关键字。</div>';
}
}
?>
</div>
</div>

<script>
$(document).ready(function() {
    // 显示错误信息的函数
    function showError(message) {
        $('#error-message').text(message);
        $('#error-container').show();
        $('#progress-container').hide();
    }

    // 隐藏错误信息的函数
    function hideError() {
        $('#error-container').hide();
    }

    // 更新进度条的函数
    function updateProgress(percentage, text) {
        $('#progress-bar').removeClass('progress-bar-animated');
        $('#progress-bar').css('width', percentage + '%').attr('aria-valuenow', percentage);
        $('#progress-text').text(text);
    }

    // 显示进度条的函数
    function showProgress() {
        $('#progress-container').show();
        $('#progress-bar').addClass('progress-bar-animated');
        updateProgress(0, '准备搜索...');
    }

    // 隐藏进度条的函数
    function hideProgress() {
        $('#progress-container').hide();
        $('#progress-bar').removeClass('progress-bar-animated');
        $('#progress-bar').css('width', '0%').attr('aria-valuenow', 0);
    }

    // 重置搜索按钮状态
    function resetSearchButton() {
        $('#search-button').attr('disabled', false);
        $('#search-text').text('搜索');
        $('#search-spinner').hide();
    }

    // 设置搜索按钮为加载状态
    function setSearchButtonLoading() {
        $('#search-button').attr('disabled', true);
        $('#search-text').text('搜索中');
        $('#search-spinner').show();
    }

    $('#search-form').submit(function(event) {
        event.preventDefault();
        var searchInput = $('[name="q"]');
        var searchValue = searchInput.val();

        // 隐藏之前的错误信息
        hideError();

        if (searchValue.trim() === "") {
            showError('请输入搜索关键字。');
            return;
        }

        // 开始搜索
        setSearchButtonLoading();
        showProgress();
        $('#search-results').empty();

        // 真实进度更新 - 通过估算搜索步骤
        setTimeout(function() {
            updateProgress(20, '正在连接搜索服务器...');
        }, 200);

        setTimeout(function() {
            updateProgress(40, '正在搜索资源...');
        }, 800);

        setTimeout(function() {
            updateProgress(60, '正在解析搜索结果...');
        }, 2000);

        setTimeout(function() {
            updateProgress(80, '正在获取资源详情...');
        }, 4000);

        $.ajax({
            type: 'POST',
            url: '',
            data: $(this).serialize(),
            timeout: 30000, // 30秒超时
            success: function(response) {
                updateProgress(95, '正在整理结果...');

                setTimeout(function() {
                    updateProgress(100, '搜索完成！');
                    $('#progress-bar').removeClass('progress-bar-animated');

                    setTimeout(function() {
                        hideProgress();
                        resetSearchButton();

                        try {
                            // 直接使用响应内容，因为PHP直接输出结果
                            var $response = $(response);
                            var searchResults = $response.find('#search-results').html();

                            if (!searchResults) {
                                // 如果没有找到#search-results，可能是直接返回的内容
                                var alertSuccess = $response.find('.alert-success').length;
                                var cardResults = $response.find('.card').length;

                                if (alertSuccess > 0 || cardResults > 0) {
                                    // 提取所有相关内容
                                    var allResults = '';
                                    $response.find('.alert-success, .card, .alert-warning').each(function() {
                                        allResults += $(this).prop('outerHTML');
                                    });
                                    $('#search-results').html(allResults);
                                } else {
                                    $('#search-results').html('<div class="alert alert-warning" role="alert">没有找到相关结果，请尝试其他关键字。</div>');
                                }
                            } else {
                                $('#search-results').html(searchResults);
                            }
                        } catch (e) {
                            showError('解析搜索结果时出现错误，请重试。');
                            console.error('解析错误:', e);
                        }
                    }, 800);
                }, 500);
            },
            error: function(xhr, status, error) {
                hideProgress();
                resetSearchButton();

                var errorMessage = '搜索失败，请重试。';
                if (status === 'timeout') {
                    errorMessage = '搜索超时，请检查网络连接后重试。';
                } else if (status === 'error') {
                    errorMessage = '网络错误，请检查网络连接。';
                } else if (xhr.status === 404) {
                    errorMessage = '搜索服务暂时不可用。';
                } else if (xhr.status === 500) {
                    errorMessage = '服务器内部错误，请稍后重试。';
                }

                showError(errorMessage);
                console.error('AJAX错误:', status, error, xhr);
            }
        });
    });

    // 点击关闭错误提示时的处理
    $(document).on('click', '.alert .close', function() {
        $(this).closest('.alert').parent().hide();
    });
});
</script>

</body>
</html>
