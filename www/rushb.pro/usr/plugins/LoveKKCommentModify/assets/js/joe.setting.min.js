document.addEventListener("DOMContentLoaded",function(){var f=document.querySelectorAll(".j-setting-tab li"),d=document.querySelector(".j-setting-notice"),g=document.querySelector("#j-version"),e=document.querySelector(".j-setting-contain > form"),h=document.querySelectorAll(".j-setting-content");f.forEach(function(a){a.addEventListener("click",function(){sessionStorage.setItem("j-setting-current",a.getAttribute("data-current"));f.forEach(function(b){return b.classList.remove("active")});a.classList.add("active");
"j-setting-notice"===a.getAttribute("data-current")?(d.style.display="block",e.style.display="none"):(e.style.display="block",d.style.display="none");h.forEach(function(b){b.style.display="none";b.classList.contains(a.getAttribute("data-current"))&&(b.style.display="block")})})});sessionStorage.getItem("j-setting-current")?("j-setting-notice"===sessionStorage.getItem("j-setting-current")?(d.style.display="block",e.style.display="none"):(e.style.display="block",d.style.display="none"),f.forEach(function(a){a.getAttribute("data-current")===
sessionStorage.getItem("j-setting-current")&&(a.classList.add("active"),h.forEach(function(b){b.classList.contains(sessionStorage.getItem("j-setting-current"))&&(b.style.display="block")}))})):(f[0].classList.add("active"),d.style.display="block",e.style.display="none");var c=new XMLHttpRequest;c.onreadystatechange=function(){if(4===c.readyState)if(200<=c.status&&300>c.status||304===c.status){var a=JSON.parse(c.responseText).tag_name;d.innerHTML=a>g.innerHTML?'<h2 class="update">\u68c0\u6d4b\u5230\u7248\u672c\u66f4\u65b0\uff01</h2><p>\u5f53\u524d\u7248\u672c\u53f7\uff1a'+
g.innerHTML+"</p><p>\u6700\u65b0\u7248\u672c\u53f7\uff1a"+a+"</p><p><a href='https://www.ijkxs.com'>\u5373\u523b\u5b66\u672f</a></p>":'<h2 class="no-update">\u5f53\u524d\u5df2\u662f\u6700\u65b0\u7248\u672c\uff01</h2><p>\u5f53\u524d\u7248\u672c\u53f7\uff1a'+g.innerHTML+"</p><p>\u6700\u65b0\u7248\u672c\u53f7\uff1a"+a+"</p><p><a href='https://www.ijkxs.com'>\u5373\u523b\u5b66\u672f</a></p>"}else d.innerHTML="\u8bf7\u6c42\u5931\u8d25\uff01"};c.open("get","https://api.github.com/repos/gogobody/LoveKKCommentModify/releases/latest",
!0);c.send(null)});
