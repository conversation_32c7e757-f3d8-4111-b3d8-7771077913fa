.v * {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    line-height: 2;
    color: #555;
    -webkit-transition: all .3s ease;
    transition: all .3s ease
}

.v hr {
    margin: .825rem 0;
    border-color: #f6f6f6;
    border-style: dashed
}

.v.hide-avatar .vimg {
    display: none
}

.v a {
    position: relative;
    cursor: pointer;
    color: #1abc9c;
    display: inline-block
}

.v a:before {
    content: "";
    position: absolute;
    width: 0;
    right: 0;
    bottom: 0;
    height: 1px;
    background: #1abc9c;
    -webkit-transition: width .3s ease;
    transition: width .3s ease
}

.v a:hover {
    color: #d7191a
}

.v a:hover:before {
    width: 100%;
    left: 0;
    right: auto
}

.v code, .v pre {
    background-color: #f6f6f6;
    color: #555;
    padding: .2em .4em;
    border-radius: 3px;
    font-size: 85%;
    margin: 0;
    font-family: Source Code Pro, courier new, Input Mono, PT Mono, SFMono-Regular, Consolas, Monaco, Menlo, PingFang SC, Liberation Mono, Microsoft YaHei, Courier，monospace
}

.v pre {
    padding: 10px;
    overflow: auto;
    line-height: 1.45
}

.v pre code {
    padding: 0;
    background: transparent;
    white-space: pre-wrap;
    word-break: keep-all
}

.v blockquote {
    color: #666;
    margin: .5rem 0;
    padding: 0 0 0 1rem;
    border-left: 8px solid hsla(0, 0%, 93%, .5)
}

.v .vinput {
    border: none;
    resize: none;
    outline: none;
    padding: 10px 5px;
    max-width: 100%;
    font-size: .775rem
}

.v input[type=checkbox], .v input[type=radio] {
    display: inline-block;
    vertical-align: middle;
    margin-top: -2px
}

.v .vwrap {
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    margin-bottom: 10px;
    overflow: hidden;
    position: relative;
    padding: 10px
}

.v .vwrap input {
    background: transparent
}

.v .vwrap .vedit {
    position: relative;
    padding-top: 10px
}

.v .vwrap .vedit .vctrl {
    text-align: right;
    font-size: 12px
}

.v .vwrap .vedit .vctrl span {
    padding: 10px;
    display: inline-block;
    vertical-align: middle;
    cursor: pointer
}

.v .vwrap .vedit .vemojis {
    display: none;
    font-size: 18px;
    text-align: justify;
    max-height: 145px;
    overflow: auto;
    margin-bottom: 10px;
    -webkit-box-shadow: 0 0 1px #f0f0f0;
    box-shadow: 0 0 1px #f0f0f0
}

.v .vwrap .vedit .vemojis i {
    font-style: normal;
    padding: 7px 0;
    width: 38px;
    cursor: pointer;
    text-align: center;
    display: inline-block;
    vertical-align: middle
}

.v .vwrap .vedit .vpreview {
    padding: 7px;
    -webkit-box-shadow: 0 0 1px #f0f0f0;
    box-shadow: 0 0 1px #f0f0f0
}

.v .vwrap .vedit .vpreview frame, .v .vwrap .vedit .vpreview iframe, .v .vwrap .vedit .vpreview img {
    max-width: 100%;
    border: none
}

.v .vwrap .vheader .vinput {
    width: 33.33%;
    border-bottom: 1px dashed #dedede
}

.v .vwrap .vheader.item2 .vinput {
    width: 50%
}

.v .vwrap .vheader.item1 .vinput {
    width: 100%
}

.v .vwrap .vheader .vinput:focus {
    border-bottom-color: #eb5055
}

@media screen and (max-width: 520px) {
    .v .vwrap .vheader.item2 .vinput, .v .vwrap .vheader .vinput {
        width: 100%
    }
}

.v .vwrap .vcontrol {
    font-size: 0;
    padding-top: 15px
}

.v .vwrap .vcontrol .col {
    display: inline-block;
    font-size: .725rem;
    vertical-align: middle;
    color: #ccc
}

.v .vwrap .vcontrol .col.text-right {
    text-align: right
}

.v .vwrap .vcontrol .col svg {
    margin-right: 2px;
    overflow: hidden;
    fill: currentColor;
    vertical-align: middle
}

.v .vwrap .vcontrol .col.col-20 {
    width: 20%
}

.v .vwrap .vcontrol .col.col-40 {
    width: 40%
}

.v .vwrap .vcontrol .col.col-60 {
    width: 60%
}

.v .vwrap .vcontrol .col.col-80 {
    width: 80%
}

.v .vwrap .vcontrol .col.split {
    width: 50%
}

.v .vwrap .vmark {
    position: absolute;
    background: rgba(0, 0, 0, .65);
    width: 100%;
    height: 100%;
    left: 0;
    top: 0
}

.v .vwrap .vmark .valert {
    padding-top: 3rem
}

.v .vwrap .vmark .valert .vtext {
    color: #fff;
    padding: 1rem 0
}

.v .vwrap .vmark .valert .vcode {
    width: 4.6875rem;
    border-radius: .3125rem;
    padding: .5rem;
    background: #dedede
}

.v .vwrap .vmark .valert .vcode:focus {
    border-color: #3090e4;
    background-color: #fff
}

@media screen and (max-width: 720px) {
    .v .vwrap .vmark .valert {
        padding-top: 5.5rem
    }

    .v .vwrap .vmark .valert .vtext {
        color: #fff;
        padding: 1rem 0
    }
}

.v .power {
    color: #999;
    padding: .5rem 0
}

.v .power, .v .power a {
    font-size: .75rem
}

.v .vinfo {
    font-size: 0;
    padding: 5px
}

.v .vinfo .col {
    font-size: .875rem;
    display: inline-block;
    width: 50%;
    vertical-align: middle
}

.v .vinfo .vcount .vnum {
    font-weight: 600;
    font-size: 1.25rem
}

.v a {
    text-decoration: none;
    color: #555
}

.v a:hover {
    color: #222
}

.v ol, .v ul {
    padding: 0;
    margin-left: 1.25rem
}

.v .txt-center {
    text-align: center
}

.v .txt-right {
    text-align: right
}

.v .pd5 {
    padding: 5px
}

.v .pd10 {
    padding: 10px
}

.v .veditor {
    width: 100%;
    min-height: 8.75rem;
    font-size: .875rem;
    background: transparent;
    resize: vertical;
    -webkit-transition: all .25s ease;
    transition: all .25s ease;
    font-family: Menlo, "Meslo LG", "Helvetica Neue", Helvetica, Arial, sans-serif, "Microsoft yahei"
}

.v .vbtn {
    -webkit-transition-duration: .4s;
    transition-duration: .4s;
    text-align: center;
    color: #313131;
    border: 1px solid #ededed;
    border-radius: .3rem;
    display: inline-block;
    background: #ededed;
    margin-bottom: 0;
    font-weight: 400;
    vertical-align: middle;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    cursor: pointer;
    white-space: nowrap;
    padding: .5rem 1.25rem;
    font-size: .875rem;
    line-height: 1.42857143;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    outline: none
}

.v .vbtn + .vbtn {
    margin-left: 1.25rem
}

.v .vbtn:active, .v .vbtn:hover {
    color: #3090e4;
    border-color: #3090e4;
    background-color: #fff
}

.v .vempty {
    padding: 1.25rem;
    text-align: center;
    color: #999
}

.v .vlist {
    width: 100%
}

.v .vlist .vcard {
    padding-top: 1.5rem;
    position: relative;
    display: block
}

.v .vlist .vcard:after {
    content: "";
    clear: both;
    display: block
}

.v .vlist .vcard .vimg {
    width: 3.125rem;
    height: 3.125rem;
    float: left;
    border-radius: 50%;
    margin-right: .7525rem;
    border: 1px solid #f5f5f5;
    padding: .125rem
}

@media screen and (max-width: 720px) {
    .v .vlist .vcard .vimg {
        width: 2.5rem;
        height: 2.5rem
    }
}

.v .vlist .vcard .vhead {
    line-height: 1.5;
    margin-top: 0
}

.v .vlist .vcard .vhead .vnick {
    position: relative;
    font-size: .875rem;
    font-weight: 500;
    margin-right: .875rem;
    cursor: pointer;
    color: #1abc9c;
    text-decoration: none;
    display: inline-block
}

.v .vlist .vcard .vhead .vnick:before {
    content: "";
    position: absolute;
    width: 0;
    right: 0;
    bottom: 0;
    height: 1px;
    background: #1abc9c;
    -webkit-transition: width .3s ease;
    transition: width .3s ease
}

.v .vlist .vcard .vhead .vnick:hover {
    color: #d7191a
}

.v .vlist .vcard .vhead .vnick:hover:before {
    width: 100%;
    left: 0;
    right: auto
}

.v .vlist .vcard .vhead .vsys {
    display: inline-block;
    padding: .2rem .5rem;
    background: #ededed;
    color: #b3b1b1;
    font-size: .75rem;
    border-radius: .2rem;
    margin-right: .3rem
}

@media screen and (max-width: 520px) {
    .v .vlist .vcard .vhead .vsys {
        display: none
    }
}

.v .vlist .vcard .vh {
    overflow: hidden;
    padding-bottom: .5rem;
    border-bottom: 1px dashed #f5f5f5
}

.v .vlist .vcard .vh .vtime {
    color: #b3b3b3;
    font-size: .75rem;
    margin-right: .875rem
}

.v .vlist .vcard .vh .vmeta {
    line-height: 1;
    position: relative
}

.v .vlist .vcard .vh .vmeta .vat {
    font-size: .8125rem;
    color: #ef2f11;
    cursor: pointer;
    float: right
}

.v .vlist .vcard .vcontent {
    word-wrap: break-word;
    word-break: break-all;
    text-align: justify;
    color: #4a4a4a;
    font-size: .875rem;
    line-height: 2;
    position: relative;
    margin-bottom: .75rem;
    padding-top: .625rem
}

.v .vlist .vcard .vcontent frame, .v .vlist .vcard .vcontent iframe, .v .vlist .vcard .vcontent img {
    max-width: 100%;
    border: none
}

.v .vlist .vcard .vcontent.expand {
    cursor: pointer;
    max-height: 11.25rem;
    overflow: hidden
}

.v .vlist .vcard .vcontent.expand:before {
    display: block;
    content: "";
    position: absolute;
    width: 100%;
    left: 0;
    top: 0;
    bottom: 3.15rem;
    pointer-events: none;
    background: -webkit-gradient(linear, left top, left bottom, from(hsla(0, 0%, 100%, 0)), to(hsla(0, 0%, 100%, .9)));
    background: linear-gradient(180deg, hsla(0, 0%, 100%, 0), hsla(0, 0%, 100%, .9))
}

.v .vlist .vcard .vcontent.expand:after {
    display: block;
    content: "Click on expand";
    text-align: center;
    color: #828586;
    position: absolute;
    width: 100%;
    height: 3.15rem;
    line-height: 3.15rem;
    left: 0;
    bottom: 0;
    pointer-events: none;
    background: hsla(0, 0%, 100%, .9)
}

.v .vlist .vcard .vquote {
    color: #666;
    margin-top: 1rem;
    padding-left: 1rem;
    border-left: 1px dashed hsla(0, 0%, 93%, .5)
}

.v .vlist .vcard .vquote .vimg {
    width: 2.225rem;
    height: 2.225rem
}

.v .vpage .vmore {
    margin: 1rem 0
}

.v .clear {
    content: "";
    display: block;
    clear: both
}

@-webkit-keyframes spin {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg)
    }
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@keyframes spin {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg)
    }
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@-webkit-keyframes pulse {
    50% {
        background: #dcdcdc
    }
}

@keyframes pulse {
    50% {
        background: #dcdcdc
    }
}

.v .vloading {
    position: relative;
    padding: 20px;
    display: block;
    height: 80px
}

.v .vloading:before {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    content: "";
    position: absolute;
    display: inline-block;
    top: 20px;
    left: 50%;
    margin-left: -20px;
    width: 40px;
    height: 40px;
    border: 6px double #a0a0a0;
    border-top-color: transparent;
    border-bottom-color: transparent;
    border-radius: 50%;
    -webkit-animation: spin 1s infinite linear;
    animation: spin 1s infinite linear
}