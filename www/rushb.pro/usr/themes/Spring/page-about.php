<?php
/**
 * 名片
 *
 * @package custom
 * <AUTHOR>
 * @link https://iobiji.com/
 * @version 1.0.0
 */
$this->need('header.php');
?>
<section class="main-hero template-about-me">
  <div class="main-hero-bg"
       style="background-image: url('<?= $this->fields->thumbnail ?>')"></div>
  <div class="d-flex flex-column align-content-center justify-content-center main-hero-content">
    <div class="text-center main-hero-content-title"><?= $this->title; ?></div>
  <div class="main-hero-waves-area waves-area">
      <svg class="waves-svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
           viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto">
        <defs>
          <path id="gentle-wave"
                d="M -160 44 c 30 0 58 -18 88 -18 s 58 18 88 18 s 58 -18 88 -18 s 58 18 88 18 v 44 h -352 Z"/>
        </defs>
        <g class="parallax">
          <use xlink:href="#gentle-wave" x="48" y="0"/>
          <use xlink:href="#gentle-wave" x="48" y="3"/>
          <use xlink:href="#gentle-wave" x="48" y="5"/>
          <use xlink:href="#gentle-wave" x="48" y="7"/>
        </g>
      </svg>
    </div>
</section>
<main class="main-content custom-about-template">
  <div class="d-flex justify-content-center align-items-center flex-column about-me-home">
    <div class="col-12 col-sm-12 col-md-5 col-lg-5 col-xl-5 about-me">
      <div class="about-me__side about-me__side--front">
        <div class="about-me__cont">
          <span class="purple">shell_exec</span><span>("<span class="green">echo</span><span class="cyan">\"</span><span class="green">Hello World!</span><span class="cyan">\"</span>");</span>
        </div>
      </div>
      <div class="about-me__side about-me__side--back">
        <!-- Back Content -->
        <div class="about-me__cta">
          <p>
            <span class="purple">$AboutMe</span> <span class="cyan">=</span> array(
            <br />
            <span class="space red">"Name"</span> <span class="cyan">=></span> <span class="green">'Fanx'</span>,
            <br/>
            <span class="space red">"Email"</span> <span class="cyan">=></span> <a href='mailto:<EMAIL>'><span class="green">'<EMAIL>'</span></a>,
            <br/>
            <span class="space red">"Position"</span> <span class="cyan">=></span> <span class="green">'Penetration Test Engineer'</span>,
            <br/>
            <span class="space red">"Website"</span> <span class="cyan">=></span> <a href='https://rushb.pro' target='_blank'><span class="green">'https://rushb.pro'</span></a>,
            <br/>
            <span class="space red">"GitHub"</span> <span class="cyan">=></span> <a href='http://github.com/FanxJK' target='_blank'><span class="green">'http://github.com/FanxJK'</span></a>
            <br/> );
          </p>
        </div>
      </div>
    </div>
  </div>
  <div class="container-sm">
    <div class="row">
      <article class="col-12 col-sm-12 px-0 post-content article-main borderbox">
        <?php echo Utils::getContent($this->content) ?>
      </article>
    </div>
  </div>
</main>