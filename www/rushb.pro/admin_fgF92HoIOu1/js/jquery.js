!function(e,t){"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:this,function(h,e){function t(e,t){return t.toUpperCase()}var n=[],c=n.slice,g=n.concat,a=n.push,i=n.indexOf,r={},o=r.toString,m=r.hasOwnProperty,y={},v=h.document,s="2.1.1-rc2",w=function(e,t){return new w.fn.init(e,t)},u=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,l=/^-ms-/,f=/-([\da-z])/gi;function p(e){var t=e.length,n=w.type(e);return"function"!==n&&!w.isWindow(e)&&(!(1!==e.nodeType||!t)||("array"===n||0===t||"number"==typeof t&&0<t&&t-1 in e))}w.fn=w.prototype={jquery:s,constructor:w,selector:"",length:0,toArray:function(){return c.call(this)},get:function(e){return null!=e?e<0?this[e+this.length]:this[e]:c.call(this)},pushStack:function(e){e=w.merge(this.constructor(),e);return e.prevObject=this,e.context=this.context,e},each:function(e,t){return w.each(this,e,t)},map:function(n){return this.pushStack(w.map(this,function(e,t){return n.call(e,t,e)}))},slice:function(){return this.pushStack(c.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,e=+e+(e<0?t:0);return this.pushStack(0<=e&&e<t?[this[e]]:[])},end:function(){return this.prevObject||this.constructor(null)},push:a,sort:n.sort,splice:n.splice},w.extend=w.fn.extend=function(){var e,t,n,r,i,o=arguments[0]||{},s=1,a=arguments.length,u=!1;for("boolean"==typeof o&&(u=o,o=arguments[s]||{},s++),"object"==typeof o||w.isFunction(o)||(o={}),s===a&&(o=this,s--);s<a;s++)if(null!=(e=arguments[s]))for(t in e)i=o[t],o!==(n=e[t])&&(u&&n&&(w.isPlainObject(n)||(r=w.isArray(n)))?(i=r?(r=!1,i&&w.isArray(i)?i:[]):i&&w.isPlainObject(i)?i:{},o[t]=w.extend(u,i,n)):void 0!==n&&(o[t]=n));return o},w.extend({expando:"jQuery"+(s+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isFunction:function(e){return"function"===w.type(e)},isArray:Array.isArray,isWindow:function(e){return null!=e&&e===e.window},isNumeric:function(e){return!w.isArray(e)&&0<=e-parseFloat(e)},isPlainObject:function(e){return"object"===w.type(e)&&!e.nodeType&&!w.isWindow(e)&&!(e.constructor&&!m.call(e.constructor.prototype,"isPrototypeOf"))},isEmptyObject:function(e){for(var t in e)return!1;return!0},type:function(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?r[o.call(e)]||"object":typeof e},globalEval:function(e){var t,n=eval;(e=w.trim(e))&&(1===e.indexOf("use strict")?((t=v.createElement("script")).text=e,v.head.appendChild(t).parentNode.removeChild(t)):n(e))},camelCase:function(e){return e.replace(l,"ms-").replace(f,t)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(e,t,n){var r=0,i=e.length,o=p(e);if(n){if(o)for(;r<i&&!1!==t.apply(e[r],n);r++);else for(r in e)if(!1===t.apply(e[r],n))break}else if(o)for(;r<i&&!1!==t.call(e[r],r,e[r]);r++);else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},trim:function(e){return null==e?"":(e+"").replace(u,"")},makeArray:function(e,t){t=t||[];return null!=e&&(p(Object(e))?w.merge(t,"string"==typeof e?[e]:e):a.call(t,e)),t},inArray:function(e,t,n){return null==t?-1:i.call(t,e,n)},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;r<n;r++)e[i++]=t[r];return e.length=i,e},grep:function(e,t,n){for(var r=[],i=0,o=e.length,s=!n;i<o;i++)!t(e[i],i)!=s&&r.push(e[i]);return r},map:function(e,t,n){var r,i=0,o=e.length,s=[];if(p(e))for(;i<o;i++)null!=(r=t(e[i],i,n))&&s.push(r);else for(i in e)null!=(r=t(e[i],i,n))&&s.push(r);return g.apply([],s)},guid:1,proxy:function(e,t){var n,r;if("string"==typeof t&&(r=e[t],t=e,e=r),w.isFunction(e))return n=c.call(arguments,2),(r=function(){return e.apply(t||this,n.concat(c.call(arguments)))}).guid=e.guid=e.guid||w.guid++,r},now:Date.now,support:y}),w.each("Boolean Number String Function Array Date RegExp Object Error".split(" "),function(e,t){r["[object "+t+"]"]=t.toLowerCase()});var d=function(n){function f(e,t,n){var r="0x"+t-65536;return r!=r||n?t:r<0?String.fromCharCode(65536+r):String.fromCharCode(r>>10|55296,1023&r|56320)}var e,p,b,o,t,d,h,g,w,l,c,m,T,r,y,v,i,s,x,C="sizzle"+-new Date,N=n.document,k=0,E=0,a=ie(),S=ie(),u=ie(),j=function(e,t){return e===t&&(c=!0),0},D="undefined",A={}.hasOwnProperty,L=[],q=L.pop,H=L.push,O=L.push,F=L.slice,P=L.indexOf||function(e){for(var t=0,n=this.length;t<n;t++)if(this[t]===e)return t;return-1},M="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",R="[\\x20\\t\\r\\n\\f]",W="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",$=W.replace("w","w#"),B="\\["+R+"*("+W+")(?:"+R+"*([*^$|!~]?=)"+R+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+$+"))|)"+R+"*\\]",I=":("+W+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+B+")*)|.*)\\)|)",_=new RegExp("^"+R+"+|((?:^|[^\\\\])(?:\\\\.)*)"+R+"+$","g"),z=new RegExp("^"+R+"*,"+R+"*"),X=new RegExp("^"+R+"*([>+~]|"+R+")"+R+"*"),U=new RegExp("="+R+"*([^\\]'\"]*?)"+R+"*\\]","g"),V=new RegExp(I),Y=new RegExp("^"+$+"$"),G={ID:new RegExp("^#("+W+")"),CLASS:new RegExp("^\\.("+W+")"),TAG:new RegExp("^("+W.replace("w","w*")+")"),ATTR:new RegExp("^"+B),PSEUDO:new RegExp("^"+I),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+R+"*(even|odd|(([+-]|)(\\d*)n|)"+R+"*(?:([+-]|)"+R+"*(\\d+)|))"+R+"*\\)|)","i"),bool:new RegExp("^(?:"+M+")$","i"),needsContext:new RegExp("^"+R+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+R+"*((?:-\\d)?\\d*)"+R+"*\\)|)(?=[^-]|$)","i")},Q=/^(?:input|select|textarea|button)$/i,J=/^h\d$/i,K=/^[^{]+\{\s*\[native \w/,Z=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ee=/[+~]/,te=/'|\\/g,ne=new RegExp("\\\\([\\da-f]{1,6}"+R+"?|("+R+")|.)","ig");try{O.apply(L=F.call(N.childNodes),N.childNodes),L[N.childNodes.length].nodeType}catch(e){O={apply:L.length?function(e,t){H.apply(e,F.call(t))}:function(e,t){for(var n=e.length,r=0;e[n++]=t[r++];);e.length=n-1}}}function re(e,t,n,r){var i,o,s,a,u,l,c;if((t?t.ownerDocument||t:N)!==T&&m(t),n=n||[],!e||"string"!=typeof e)return n;if(1!==(i=(t=t||T).nodeType)&&9!==i)return[];if(y&&!r){if(l=Z.exec(e))if(c=l[1]){if(9===i){if(!(a=t.getElementById(c))||!a.parentNode)return n;if(a.id===c)return n.push(a),n}else if(t.ownerDocument&&(a=t.ownerDocument.getElementById(c))&&x(t,a)&&a.id===c)return n.push(a),n}else{if(l[2])return O.apply(n,t.getElementsByTagName(e)),n;if((c=l[3])&&p.getElementsByClassName&&t.getElementsByClassName)return O.apply(n,t.getElementsByClassName(c)),n}if(p.qsa&&(!v||!v.test(e))){if(u=a=C,l=t,c=9===i&&e,1===i&&"object"!==t.nodeName.toLowerCase()){for(s=d(e),(a=t.getAttribute("id"))?u=a.replace(te,"\\$&"):t.setAttribute("id",u),u="[id='"+u+"'] ",o=s.length;o--;)s[o]=u+pe(s[o]);l=ee.test(e)&&ce(t.parentNode)||t,c=s.join(",")}if(c)try{return O.apply(n,l.querySelectorAll(c)),n}catch(e){}finally{a||t.removeAttribute("id")}}}return g(e.replace(_,"$1"),t,n,r)}function ie(){var n=[];function r(e,t){return n.push(e+" ")>b.cacheLength&&delete r[n.shift()],r[e+" "]=t}return r}function oe(e){return e[C]=!0,e}function se(e){var t=T.createElement("div");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t)}}function ae(e,t){for(var n=e.split("|"),r=e.length;r--;)b.attrHandle[n[r]]=t}function ue(e,t){var n=t&&e,r=n&&1===e.nodeType&&1===t.nodeType&&(~t.sourceIndex||1<<31)-(~e.sourceIndex||1<<31);if(r)return r;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function le(s){return oe(function(o){return o=+o,oe(function(e,t){for(var n,r=s([],e.length,o),i=r.length;i--;)e[n=r[i]]&&(e[n]=!(t[n]=e[n]))})})}function ce(e){return e&&typeof e.getElementsByTagName!==D&&e}for(e in p=re.support={},t=re.isXML=function(e){e=e&&(e.ownerDocument||e).documentElement;return!!e&&"HTML"!==e.nodeName},m=re.setDocument=function(e){var u=e?e.ownerDocument||e:N,e=u.defaultView;return u!==T&&9===u.nodeType&&u.documentElement?(r=(T=u).documentElement,y=!t(u),e&&e!==e.top&&(e.addEventListener?e.addEventListener("unload",function(){m()},!1):e.attachEvent&&e.attachEvent("onunload",function(){m()})),p.attributes=se(function(e){return e.className="i",!e.getAttribute("className")}),p.getElementsByTagName=se(function(e){return e.appendChild(u.createComment("")),!e.getElementsByTagName("*").length}),p.getElementsByClassName=K.test(u.getElementsByClassName)&&se(function(e){return e.innerHTML="<div class='a'></div><div class='a i'></div>",e.firstChild.className="i",2===e.getElementsByClassName("i").length}),p.getById=se(function(e){return r.appendChild(e).id=C,!u.getElementsByName||!u.getElementsByName(C).length}),p.getById?(b.find.ID=function(e,t){if(typeof t.getElementById!==D&&y){e=t.getElementById(e);return e&&e.parentNode?[e]:[]}},b.filter.ID=function(e){var t=e.replace(ne,f);return function(e){return e.getAttribute("id")===t}}):(delete b.find.ID,b.filter.ID=function(e){var t=e.replace(ne,f);return function(e){e=typeof e.getAttributeNode!==D&&e.getAttributeNode("id");return e&&e.value===t}}),b.find.TAG=p.getElementsByTagName?function(e,t){if(typeof t.getElementsByTagName!==D)return t.getElementsByTagName(e)}:function(e,t){var n,r=[],i=0,o=t.getElementsByTagName(e);if("*"!==e)return o;for(;n=o[i++];)1===n.nodeType&&r.push(n);return r},b.find.CLASS=p.getElementsByClassName&&function(e,t){if(typeof t.getElementsByClassName!==D&&y)return t.getElementsByClassName(e)},i=[],v=[],(p.qsa=K.test(u.querySelectorAll))&&(se(function(e){e.innerHTML="<select msallowclip=''><option selected=''></option></select>",e.querySelectorAll("[msallowclip^='']").length&&v.push("[*^$]="+R+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||v.push("\\["+R+"*(?:value|"+M+")"),e.querySelectorAll(":checked").length||v.push(":checked")}),se(function(e){var t=u.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&v.push("name"+R+"*[*^$|!~]?="),e.querySelectorAll(":enabled").length||v.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),v.push(",.*:")})),(p.matchesSelector=K.test(s=r.matches||r.webkitMatchesSelector||r.mozMatchesSelector||r.oMatchesSelector||r.msMatchesSelector))&&se(function(e){p.disconnectedMatch=s.call(e,"div"),s.call(e,"[s!='']:x"),i.push("!=",I)}),v=v.length&&new RegExp(v.join("|")),i=i.length&&new RegExp(i.join("|")),e=K.test(r.compareDocumentPosition),x=e||K.test(r.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,t=t&&t.parentNode;return e===t||!(!t||1!==t.nodeType||!(n.contains?n.contains(t):e.compareDocumentPosition&&16&e.compareDocumentPosition(t)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},j=e?function(e,t){if(e===t)return c=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(1&(n=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!p.sortDetached&&t.compareDocumentPosition(e)===n?e===u||e.ownerDocument===N&&x(N,e)?-1:t===u||t.ownerDocument===N&&x(N,t)?1:l?P.call(l,e)-P.call(l,t):0:4&n?-1:1)}:function(e,t){if(e===t)return c=!0,0;var n,r=0,i=e.parentNode,o=t.parentNode,s=[e],a=[t];if(!i||!o)return e===u?-1:t===u?1:i?-1:o?1:l?P.call(l,e)-P.call(l,t):0;if(i===o)return ue(e,t);for(n=e;n=n.parentNode;)s.unshift(n);for(n=t;n=n.parentNode;)a.unshift(n);for(;s[r]===a[r];)r++;return r?ue(s[r],a[r]):s[r]===N?-1:a[r]===N?1:0},u):T},re.matches=function(e,t){return re(e,null,null,t)},re.matchesSelector=function(e,t){if((e.ownerDocument||e)!==T&&m(e),t=t.replace(U,"='$1']"),p.matchesSelector&&y&&(!i||!i.test(t))&&(!v||!v.test(t)))try{var n=s.call(e,t);if(n||p.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){}return 0<re(t,T,null,[e]).length},re.contains=function(e,t){return(e.ownerDocument||e)!==T&&m(e),x(e,t)},re.attr=function(e,t){(e.ownerDocument||e)!==T&&m(e);var n=b.attrHandle[t.toLowerCase()],n=n&&A.call(b.attrHandle,t.toLowerCase())?n(e,t,!y):void 0;return void 0!==n?n:p.attributes||!y?e.getAttribute(t):(n=e.getAttributeNode(t))&&n.specified?n.value:null},re.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},re.uniqueSort=function(e){var t,n=[],r=0,i=0;if(c=!p.detectDuplicates,l=!p.sortStable&&e.slice(0),e.sort(j),c){for(;t=e[i++];)t===e[i]&&(r=n.push(i));for(;r--;)e.splice(n[r],1)}return l=null,e},o=re.getText=function(e){var t,n="",r=0,i=e.nodeType;if(i){if(1===i||9===i||11===i){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=o(e)}else if(3===i||4===i)return e.nodeValue}else for(;t=e[r++];)n+=o(t);return n},(b=re.selectors={cacheLength:50,createPseudo:oe,match:G,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(ne,f),e[3]=(e[3]||e[4]||e[5]||"").replace(ne,f),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||re.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&re.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return G.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&V.test(n)&&(t=d(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(ne,f).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=a[e+" "];return t||(t=new RegExp("(^|"+R+")"+e+"("+R+"|$)"))&&a(e,function(e){return t.test("string"==typeof e.className&&e.className||typeof e.getAttribute!==D&&e.getAttribute("class")||"")})},ATTR:function(t,n,r){return function(e){e=re.attr(e,t);return null==e?"!="===n:!n||(e+="","="===n?e===r:"!="===n?e!==r:"^="===n?r&&0===e.indexOf(r):"*="===n?r&&-1<e.indexOf(r):"$="===n?r&&e.slice(-r.length)===r:"~="===n?-1<(" "+e+" ").indexOf(r):"|="===n&&(e===r||e.slice(0,r.length+1)===r+"-"))}},CHILD:function(d,e,t,h,g){var m="nth"!==d.slice(0,3),y="last"!==d.slice(-4),v="of-type"===e;return 1===h&&0===g?function(e){return!!e.parentNode}:function(e,t,n){var r,i,o,s,a,u,l=m!=y?"nextSibling":"previousSibling",c=e.parentNode,f=v&&e.nodeName.toLowerCase(),p=!n&&!v;if(c){if(m){for(;l;){for(o=e;o=o[l];)if(v?o.nodeName.toLowerCase()===f:1===o.nodeType)return!1;u=l="only"===d&&!u&&"nextSibling"}return!0}if(u=[y?c.firstChild:c.lastChild],y&&p){for(a=(r=(i=c[C]||(c[C]={}))[d]||[])[0]===k&&r[1],s=r[0]===k&&r[2],o=a&&c.childNodes[a];o=++a&&o&&o[l]||(s=a=0)||u.pop();)if(1===o.nodeType&&++s&&o===e){i[d]=[k,a,s];break}}else if(p&&(r=(e[C]||(e[C]={}))[d])&&r[0]===k)s=r[1];else for(;(o=++a&&o&&o[l]||(s=a=0)||u.pop())&&((v?o.nodeName.toLowerCase()!==f:1!==o.nodeType)||!++s||(p&&((o[C]||(o[C]={}))[d]=[k,s]),o!==e)););return(s-=g)===h||s%h==0&&0<=s/h}}},PSEUDO:function(e,o){var t,s=b.pseudos[e]||b.setFilters[e.toLowerCase()]||re.error("unsupported pseudo: "+e);return s[C]?s(o):1<s.length?(t=[e,e,"",o],b.setFilters.hasOwnProperty(e.toLowerCase())?oe(function(e,t){for(var n,r=s(e,o),i=r.length;i--;)e[n=P.call(e,r[i])]=!(t[n]=r[i])}):function(e){return s(e,0,t)}):s}},pseudos:{not:oe(function(e){var r=[],i=[],a=h(e.replace(_,"$1"));return a[C]?oe(function(e,t,n,r){for(var i,o=a(e,null,r,[]),s=e.length;s--;)(i=o[s])&&(e[s]=!(t[s]=i))}):function(e,t,n){return r[0]=e,a(r,null,n,i),!i.pop()}}),has:oe(function(t){return function(e){return 0<re(t,e).length}}),contains:oe(function(t){return function(e){return-1<(e.textContent||e.innerText||o(e)).indexOf(t)}}),lang:oe(function(n){return Y.test(n||"")||re.error("unsupported lang: "+n),n=n.replace(ne,f).toLowerCase(),function(e){var t;do{if(t=y?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(t=t.toLowerCase())===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=n.location&&n.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===r},focus:function(e){return e===T.activeElement&&(!T.hasFocus||T.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:function(e){return!1===e.disabled},disabled:function(e){return!0===e.disabled},checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!b.pseudos.empty(e)},header:function(e){return J.test(e.nodeName)},input:function(e){return Q.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(e=e.getAttribute("type"))||"text"===e.toLowerCase())},first:le(function(){return[0]}),last:le(function(e,t){return[t-1]}),eq:le(function(e,t,n){return[n<0?n+t:n]}),even:le(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:le(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:le(function(e,t,n){for(var r=n<0?n+t:n;0<=--r;)e.push(r);return e}),gt:le(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}}).pseudos.nth=b.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})b.pseudos[e]=function(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}(e);for(e in{submit:!0,reset:!0})b.pseudos[e]=function(n){return function(e){var t=e.nodeName.toLowerCase();return("input"===t||"button"===t)&&e.type===n}}(e);function fe(){}function pe(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function de(s,e,t){var a=e.dir,u=t&&"parentNode"===a,l=E++;return e.first?function(e,t,n){for(;e=e[a];)if(1===e.nodeType||u)return s(e,t,n)}:function(e,t,n){var r,i,o=[k,l];if(n){for(;e=e[a];)if((1===e.nodeType||u)&&s(e,t,n))return!0}else for(;e=e[a];)if(1===e.nodeType||u){if((r=(i=e[C]||(e[C]={}))[a])&&r[0]===k&&r[1]===l)return o[2]=r[2];if((i[a]=o)[2]=s(e,t,n))return!0}}}function he(i){return 1<i.length?function(e,t,n){for(var r=i.length;r--;)if(!i[r](e,t,n))return!1;return!0}:i[0]}function ge(e,t,n,r,i){for(var o,s=[],a=0,u=e.length,l=null!=t;a<u;a++)(o=e[a])&&(n&&!n(o,r,i)||(s.push(o),l&&t.push(a)));return s}function me(d,h,g,m,y,e){return m&&!m[C]&&(m=me(m)),y&&!y[C]&&(y=me(y,e)),oe(function(e,t,n,r){var i,o,s,a=[],u=[],l=t.length,c=e||function(e,t,n){for(var r=0,i=t.length;r<i;r++)re(e,t[r],n);return n}(h||"*",n.nodeType?[n]:n,[]),f=!d||!e&&h?c:ge(c,a,d,n,r),p=g?y||(e?d:l||m)?[]:t:f;if(g&&g(f,p,n,r),m)for(i=ge(p,u),m(i,[],n,r),o=i.length;o--;)(s=i[o])&&(p[u[o]]=!(f[u[o]]=s));if(e){if(y||d){if(y){for(i=[],o=p.length;o--;)(s=p[o])&&i.push(f[o]=s);y(null,p=[],i,r)}for(o=p.length;o--;)(s=p[o])&&-1<(i=y?P.call(e,s):a[o])&&(e[i]=!(t[i]=s))}}else p=ge(p===t?p.splice(l,p.length):p),y?y(null,t,p,r):O.apply(t,p)})}function ye(m,y){function e(e,t,n,r,i){var o,s,a,u=0,l="0",c=e&&[],f=[],p=w,d=e||x&&b.find.TAG("*",i),h=k+=null==p?1:Math.random()||.1,g=d.length;for(i&&(w=t!==T&&t);l!==g&&null!=(o=d[l]);l++){if(x&&o){for(s=0;a=m[s++];)if(a(o,t,n)){r.push(o);break}i&&(k=h)}v&&((o=!a&&o)&&u--,e&&c.push(o))}if(u+=l,v&&l!==u){for(s=0;a=y[s++];)a(c,f,t,n);if(e){if(0<u)for(;l--;)c[l]||f[l]||(f[l]=q.call(r));f=ge(f)}O.apply(r,f),i&&!e&&0<f.length&&1<u+y.length&&re.uniqueSort(r)}return i&&(k=h,w=p),c}var v=0<y.length,x=0<m.length;return v?oe(e):e}return fe.prototype=b.filters=b.pseudos,b.setFilters=new fe,d=re.tokenize=function(e,t){var n,r,i,o,s,a,u,l=S[e+" "];if(l)return t?0:l.slice(0);for(s=e,a=[],u=b.preFilter;s;){for(o in n&&!(r=z.exec(s))||(r&&(s=s.slice(r[0].length)||s),a.push(i=[])),n=!1,(r=X.exec(s))&&(n=r.shift(),i.push({value:n,type:r[0].replace(_," ")}),s=s.slice(n.length)),b.filter)!(r=G[o].exec(s))||u[o]&&!(r=u[o](r))||(n=r.shift(),i.push({value:n,type:o,matches:r}),s=s.slice(n.length));if(!n)break}return t?s.length:s?re.error(e):S(e,a).slice(0)},h=re.compile=function(e,t){var n,r=[],i=[],o=u[e+" "];if(!o){for(n=(t=t||d(e)).length;n--;)((o=function e(t){for(var r,n,i,o=t.length,s=b.relative[t[0].type],a=s||b.relative[" "],u=s?1:0,l=de(function(e){return e===r},a,!0),c=de(function(e){return-1<P.call(r,e)},a,!0),f=[function(e,t,n){return!s&&(n||t!==w)||((r=t).nodeType?l:c)(e,t,n)}];u<o;u++)if(n=b.relative[t[u].type])f=[de(he(f),n)];else{if((n=b.filter[t[u].type].apply(null,t[u].matches))[C]){for(i=++u;i<o&&!b.relative[t[i].type];i++);return me(1<u&&he(f),1<u&&pe(t.slice(0,u-1).concat({value:" "===t[u-2].type?"*":""})).replace(_,"$1"),n,u<i&&e(t.slice(u,i)),i<o&&e(t=t.slice(i)),i<o&&pe(t))}f.push(n)}return he(f)}(t[n]))[C]?r:i).push(o);(o=u(e,ye(i,r))).selector=e}return o},g=re.select=function(e,t,n,r){var i,o,s,a,u,l="function"==typeof e&&e,c=!r&&d(e=l.selector||e);if(n=n||[],1===c.length){if(2<(o=c[0]=c[0].slice(0)).length&&"ID"===(s=o[0]).type&&p.getById&&9===t.nodeType&&y&&b.relative[o[1].type]){if(!(t=(b.find.ID(s.matches[0].replace(ne,f),t)||[])[0]))return n;l&&(t=t.parentNode),e=e.slice(o.shift().value.length)}for(i=G.needsContext.test(e)?0:o.length;i--&&(s=o[i],!b.relative[a=s.type]);)if((u=b.find[a])&&(r=u(s.matches[0].replace(ne,f),ee.test(o[0].type)&&ce(t.parentNode)||t))){if(o.splice(i,1),!(e=r.length&&pe(o)))return O.apply(n,r),n;break}}return(l||h(e,c))(r,t,!y,n,ee.test(e)&&ce(t.parentNode)||t),n},p.sortStable=C.split("").sort(j).join("")===C,p.detectDuplicates=!!c,m(),p.sortDetached=se(function(e){return 1&e.compareDocumentPosition(T.createElement("div"))}),se(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||ae("type|href|height|width",function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),p.attributes&&se(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||ae("value",function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue}),se(function(e){return null==e.getAttribute("disabled")})||ae(M,function(e,t,n){if(!n)return!0===e[t]?t.toLowerCase():(t=e.getAttributeNode(t))&&t.specified?t.value:null}),re}(h);w.find=d,w.expr=d.selectors,w.expr[":"]=w.expr.pseudos,w.unique=d.uniqueSort,w.text=d.getText,w.isXMLDoc=d.isXML,w.contains=d.contains;var x=w.expr.match.needsContext,b=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,T=/^.[^:#\[\.,]*$/;function C(e,n,r){if(w.isFunction(n))return w.grep(e,function(e,t){return!!n.call(e,t,e)!==r});if(n.nodeType)return w.grep(e,function(e){return e===n!==r});if("string"==typeof n){if(T.test(n))return w.filter(n,e,r);n=w.filter(n,e)}return w.grep(e,function(e){return 0<=i.call(n,e)!==r})}w.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?w.find.matchesSelector(r,e)?[r]:[]:w.find.matches(e,w.grep(t,function(e){return 1===e.nodeType}))},w.fn.extend({find:function(e){var t,n=this.length,r=[],i=this;if("string"!=typeof e)return this.pushStack(w(e).filter(function(){for(t=0;t<n;t++)if(w.contains(i[t],this))return!0}));for(t=0;t<n;t++)w.find(e,i[t],r);return(r=this.pushStack(1<n?w.unique(r):r)).selector=this.selector?this.selector+" "+e:e,r},filter:function(e){return this.pushStack(C(this,e||[],!1))},not:function(e){return this.pushStack(C(this,e||[],!0))},is:function(e){return!!C(this,"string"==typeof e&&x.test(e)?w(e):e||[],!1).length}});var N=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/;(w.fn.init=function(e,t){var n,r;if(!e)return this;if("string"!=typeof e)return e.nodeType?(this.context=this[0]=e,this.length=1,this):w.isFunction(e)?void 0!==k.ready?k.ready(e):e(w):(void 0!==e.selector&&(this.selector=e.selector,this.context=e.context),w.makeArray(e,this));if(!(n="<"===e[0]&&">"===e[e.length-1]&&3<=e.length?[null,e,null]:N.exec(e))||!n[1]&&t)return(!t||t.jquery?t||k:this.constructor(t)).find(e);if(n[1]){if(t=t instanceof w?t[0]:t,w.merge(this,w.parseHTML(n[1],t&&t.nodeType?t.ownerDocument||t:v,!0)),b.test(n[1])&&w.isPlainObject(t))for(n in t)w.isFunction(this[n])?this[n](t[n]):this.attr(n,t[n]);return this}return(r=v.getElementById(n[2]))&&r.parentNode&&(this.length=1,this[0]=r),this.context=v,this.selector=e,this}).prototype=w.fn;var k=w(v),E=/^(?:parents|prev(?:Until|All))/,S={children:!0,contents:!0,next:!0,prev:!0};function j(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}w.extend({dir:function(e,t,n){for(var r=[],i=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(i&&w(e).is(n))break;r.push(e)}return r},sibling:function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}}),w.fn.extend({has:function(e){var t=w(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(w.contains(this,t[e]))return!0})},closest:function(e,t){for(var n,r=0,i=this.length,o=[],s=x.test(e)||"string"!=typeof e?w(e,t||this.context):0;r<i;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(s?-1<s.index(n):1===n.nodeType&&w.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(1<o.length?w.unique(o):o)},index:function(e){return e?"string"==typeof e?i.call(w(e),this[0]):i.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(w.unique(w.merge(this.get(),w(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),w.each({parent:function(e){e=e.parentNode;return e&&11!==e.nodeType?e:null},parents:function(e){return w.dir(e,"parentNode")},parentsUntil:function(e,t,n){return w.dir(e,"parentNode",n)},next:function(e){return j(e,"nextSibling")},prev:function(e){return j(e,"previousSibling")},nextAll:function(e){return w.dir(e,"nextSibling")},prevAll:function(e){return w.dir(e,"previousSibling")},nextUntil:function(e,t,n){return w.dir(e,"nextSibling",n)},prevUntil:function(e,t,n){return w.dir(e,"previousSibling",n)},siblings:function(e){return w.sibling((e.parentNode||{}).firstChild,e)},children:function(e){return w.sibling(e.firstChild)},contents:function(e){return e.contentDocument||w.merge([],e.childNodes)}},function(r,i){w.fn[r]=function(e,t){var n=w.map(this,i,e);return(t="Until"!==r.slice(-5)?e:t)&&"string"==typeof t&&(n=w.filter(t,n)),1<this.length&&(S[r]||w.unique(n),E.test(r)&&n.reverse()),this.pushStack(n)}});var D,A=/\S+/g,L={};function q(){v.removeEventListener("DOMContentLoaded",q,!1),h.removeEventListener("load",q,!1),w.ready()}w.Callbacks=function(i){var e,n;i="string"==typeof i?L[i]||(n=L[e=i]={},w.each(e.match(A)||[],function(e,t){n[t]=!0}),n):w.extend({},i);function r(e){for(t=i.memory&&e,o=!0,l=a||0,a=0,u=c.length,s=!0;c&&l<u;l++)if(!1===c[l].apply(e[0],e[1])&&i.stopOnFalse){t=!1;break}s=!1,c&&(f?f.length&&r(f.shift()):t?c=[]:p.disable())}var t,o,s,a,u,l,c=[],f=!i.once&&[],p={add:function(){var e;return c&&(e=c.length,function r(e){w.each(e,function(e,t){var n=w.type(t);"function"===n?i.unique&&p.has(t)||c.push(t):t&&t.length&&"string"!==n&&r(t)})}(arguments),s?u=c.length:t&&(a=e,r(t))),this},remove:function(){return c&&w.each(arguments,function(e,t){for(var n;-1<(n=w.inArray(t,c,n));)c.splice(n,1),s&&(n<=u&&u--,n<=l&&l--)}),this},has:function(e){return e?-1<w.inArray(e,c):!(!c||!c.length)},empty:function(){return c=[],u=0,this},disable:function(){return c=f=t=void 0,this},disabled:function(){return!c},lock:function(){return f=void 0,t||p.disable(),this},locked:function(){return!f},fireWith:function(e,t){return!c||o&&!f||(t=[e,(t=t||[]).slice?t.slice():t],s?f.push(t):r(t)),this},fire:function(){return p.fireWith(this,arguments),this},fired:function(){return!!o}};return p},w.extend({Deferred:function(e){var o=[["resolve","done",w.Callbacks("once memory"),"resolved"],["reject","fail",w.Callbacks("once memory"),"rejected"],["notify","progress",w.Callbacks("memory")]],i="pending",s={state:function(){return i},always:function(){return a.done(arguments).fail(arguments),this},then:function(){var i=arguments;return w.Deferred(function(r){w.each(o,function(e,t){var n=w.isFunction(i[e])&&i[e];a[t[1]](function(){var e=n&&n.apply(this,arguments);e&&w.isFunction(e.promise)?e.promise().done(r.resolve).fail(r.reject).progress(r.notify):r[t[0]+"With"](this===s?r.promise():this,n?[e]:arguments)})}),i=null}).promise()},promise:function(e){return null!=e?w.extend(e,s):s}},a={};return s.pipe=s.then,w.each(o,function(e,t){var n=t[2],r=t[3];s[t[1]]=n.add,r&&n.add(function(){i=r},o[1^e][2].disable,o[2][2].lock),a[t[0]]=function(){return a[t[0]+"With"](this===a?s:this,arguments),this},a[t[0]+"With"]=n.fireWith}),s.promise(a),e&&e.call(a,a),a},when:function(e){function t(t,n,r){return function(e){n[t]=this,r[t]=1<arguments.length?c.call(arguments):e,r===i?l.notifyWith(n,r):--u||l.resolveWith(n,r)}}var i,n,r,o=0,s=c.call(arguments),a=s.length,u=1!==a||e&&w.isFunction(e.promise)?a:0,l=1===u?e:w.Deferred();if(1<a)for(i=new Array(a),n=new Array(a),r=new Array(a);o<a;o++)s[o]&&w.isFunction(s[o].promise)?s[o].promise().done(t(o,r,s)).fail(l.reject).progress(t(o,n,i)):--u;return u||l.resolveWith(r,s),l.promise()}}),w.fn.ready=function(e){return w.ready.promise().done(e),this},w.extend({isReady:!1,readyWait:1,holdReady:function(e){e?w.readyWait++:w.ready(!0)},ready:function(e){(!0===e?--w.readyWait:w.isReady)||(w.isReady=!0)!==e&&0<--w.readyWait||(D.resolveWith(v,[w]),w.fn.triggerHandler&&(w(v).triggerHandler("ready"),w(v).off("ready")))}}),w.ready.promise=function(e){return D||(D=w.Deferred(),"complete"===v.readyState?setTimeout(w.ready):(v.addEventListener("DOMContentLoaded",q,!1),h.addEventListener("load",q,!1))),D.promise(e)},w.ready.promise();var H=w.access=function(e,t,n,r,i,o,s){var a=0,u=e.length,l=null==n;if("object"===w.type(n))for(a in i=!0,n)w.access(e,t,a,n[a],!0,o,s);else if(void 0!==r&&(i=!0,w.isFunction(r)||(s=!0),t=l?s?(t.call(e,r),null):(l=t,function(e,t,n){return l.call(w(e),n)}):t))for(;a<u;a++)t(e[a],n,s?r:r.call(e[a],a,t(e[a],n)));return i?e:l?t.call(e):u?t(e[0],n):o};function O(){Object.defineProperty(this.cache={},0,{get:function(){return{}}}),this.expando=w.expando+Math.random()}w.acceptData=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType},O.uid=1,O.accepts=w.acceptData,O.prototype={key:function(t){if(!O.accepts(t))return 0;var n={},r=t[this.expando];if(!r){r=O.uid++;try{n[this.expando]={value:r},Object.defineProperties(t,n)}catch(e){n[this.expando]=r,w.extend(t,n)}}return this.cache[r]||(this.cache[r]={}),r},set:function(e,t,n){var r,e=this.key(e),i=this.cache[e];if("string"==typeof t)i[t]=n;else if(w.isEmptyObject(i))w.extend(this.cache[e],t);else for(r in t)i[r]=t[r];return i},get:function(e,t){e=this.cache[this.key(e)];return void 0===t?e:e[t]},access:function(e,t,n){var r;return void 0===t||t&&"string"==typeof t&&void 0===n?void 0!==(r=this.get(e,t))?r:this.get(e,w.camelCase(t)):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r,e=this.key(e),i=this.cache[e];if(void 0===t)this.cache[e]={};else{n=(r=w.isArray(t)?t.concat(t.map(w.camelCase)):(e=w.camelCase(t),t in i?[t,e]:(r=e)in i?[r]:r.match(A)||[])).length;for(;n--;)delete i[r[n]]}},hasData:function(e){return!w.isEmptyObject(this.cache[e[this.expando]]||{})},discard:function(e){e[this.expando]&&delete this.cache[e[this.expando]]}};var F=new O,P=new O,M=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,R=/([A-Z])/g;function W(e,t,n){var r;if(void 0===n&&1===e.nodeType)if(r="data-"+t.replace(R,"-$1").toLowerCase(),"string"==typeof(n=e.getAttribute(r))){try{n="true"===n||"false"!==n&&("null"===n?null:+n+""===n?+n:M.test(n)?w.parseJSON(n):n)}catch(e){}P.set(e,t,n)}else n=void 0;return n}w.extend({hasData:function(e){return P.hasData(e)||F.hasData(e)},data:function(e,t,n){return P.access(e,t,n)},removeData:function(e,t){P.remove(e,t)},_data:function(e,t,n){return F.access(e,t,n)},_removeData:function(e,t){F.remove(e,t)}}),w.fn.extend({data:function(r,e){var t,n,i,o=this[0],s=o&&o.attributes;if(void 0!==r)return"object"==typeof r?this.each(function(){P.set(this,r)}):H(this,function(t){var e,n=w.camelCase(r);if(o&&void 0===t)return void 0!==(e=P.get(o,r))||void 0!==(e=P.get(o,n))||void 0!==(e=W(o,n,void 0))?e:void 0;this.each(function(){var e=P.get(this,n);P.set(this,n,t),-1!==r.indexOf("-")&&void 0!==e&&P.set(this,r,t)})},null,e,1<arguments.length,null,!0);if(this.length&&(i=P.get(o),1===o.nodeType&&!F.get(o,"hasDataAttrs"))){for(t=s.length;t--;)0===(n=s[t].name).indexOf("data-")&&(n=w.camelCase(n.slice(5)),W(o,n,i[n]));F.set(o,"hasDataAttrs",!0)}return i},removeData:function(e){return this.each(function(){P.remove(this,e)})}}),w.extend({queue:function(e,t,n){var r;if(e)return r=F.get(e,t=(t||"fx")+"queue"),n&&(!r||w.isArray(n)?r=F.access(e,t,w.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=w.queue(e,t),r=n.length,i=n.shift(),o=w._queueHooks(e,t);"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,function(){w.dequeue(e,t)},o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return F.get(e,n)||F.access(e,n,{empty:w.Callbacks("once memory").add(function(){F.remove(e,[t+"queue",n])})})}}),w.fn.extend({queue:function(t,n){var e=2;return"string"!=typeof t&&(n=t,t="fx",e--),arguments.length<e?w.queue(this[0],t):void 0===n?this:this.each(function(){var e=w.queue(this,t,n);w._queueHooks(this,t),"fx"===t&&"inprogress"!==e[0]&&w.dequeue(this,t)})},dequeue:function(e){return this.each(function(){w.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){function n(){--i||o.resolveWith(s,[s])}var r,i=1,o=w.Deferred(),s=this,a=this.length;for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";a--;)(r=F.get(s[a],e+"queueHooks"))&&r.empty&&(i++,r.empty.add(n));return n(),o.promise(t)}});function $(e,t){return"none"===w.css(e=t||e,"display")||!w.contains(e.ownerDocument,e)}var n=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,B=["Top","Right","Bottom","Left"],I=/^(?:checkbox|radio)$/i;s=v.createDocumentFragment().appendChild(v.createElement("div")),(d=v.createElement("input")).setAttribute("type","radio"),d.setAttribute("checked","checked"),d.setAttribute("name","t"),s.appendChild(d),y.checkClone=s.cloneNode(!0).cloneNode(!0).lastChild.checked,s.innerHTML="<textarea>x</textarea>",y.noCloneChecked=!!s.cloneNode(!0).lastChild.defaultValue;var _="undefined";y.focusinBubbles="onfocusin"in h;var z=/^key/,X=/^(?:mouse|pointer|contextmenu)|click/,U=/^(?:focusinfocus|focusoutblur)$/,V=/^([^.]*)(?:\.(.+)|)$/;function Y(){return!0}function G(){return!1}function Q(){try{return v.activeElement}catch(e){}}w.event={global:{},add:function(t,e,n,r,i){var o,s,a,u,l,c,f,p,d,h=F.get(t);if(h)for(n.handler&&(n=(o=n).handler,i=o.selector),n.guid||(n.guid=w.guid++),(a=h.events)||(a=h.events={}),(s=h.handle)||(s=h.handle=function(e){return typeof w!=_&&w.event.triggered!==e.type?w.event.dispatch.apply(t,arguments):void 0}),u=(e=(e||"").match(A)||[""]).length;u--;)f=d=(l=V.exec(e[u])||[])[1],p=(l[2]||"").split(".").sort(),f&&(c=w.event.special[f]||{},f=(i?c.delegateType:c.bindType)||f,c=w.event.special[f]||{},l=w.extend({type:f,origType:d,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&w.expr.match.needsContext.test(i),namespace:p.join(".")},o),(d=a[f])||((d=a[f]=[]).delegateCount=0,c.setup&&!1!==c.setup.call(t,r,p,s)||t.addEventListener&&t.addEventListener(f,s,!1)),c.add&&(c.add.call(t,l),l.handler.guid||(l.handler.guid=n.guid)),i?d.splice(d.delegateCount++,0,l):d.push(l),w.event.global[f]=!0)},remove:function(e,t,n,r,i){var o,s,a,u,l,c,f,p,d,h,g,m=F.hasData(e)&&F.get(e);if(m&&(u=m.events)){for(l=(t=(t||"").match(A)||[""]).length;l--;)if(d=g=(a=V.exec(t[l])||[])[1],h=(a[2]||"").split(".").sort(),d){for(f=w.event.special[d]||{},p=u[d=(r?f.delegateType:f.bindType)||d]||[],a=a[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=o=p.length;o--;)c=p[o],!i&&g!==c.origType||n&&n.guid!==c.guid||a&&!a.test(c.namespace)||r&&r!==c.selector&&("**"!==r||!c.selector)||(p.splice(o,1),c.selector&&p.delegateCount--,f.remove&&f.remove.call(e,c));s&&!p.length&&(f.teardown&&!1!==f.teardown.call(e,h,m.handle)||w.removeEvent(e,d,m.handle),delete u[d])}else for(d in u)w.event.remove(e,d+t[l],n,r,!0);w.isEmptyObject(u)&&(delete m.handle,F.remove(e,"events"))}},trigger:function(e,t,n,r){var i,o,s,a,u,l,c=[n||v],f=m.call(e,"type")?e.type:e,p=m.call(e,"namespace")?e.namespace.split("."):[],d=o=n=n||v;if(3!==n.nodeType&&8!==n.nodeType&&!U.test(f+w.event.triggered)&&(0<=f.indexOf(".")&&(f=(p=f.split(".")).shift(),p.sort()),a=f.indexOf(":")<0&&"on"+f,(e=e[w.expando]?e:new w.Event(f,"object"==typeof e&&e)).isTrigger=r?2:3,e.namespace=p.join("."),e.namespace_re=e.namespace?new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:w.makeArray(t,[e]),l=w.event.special[f]||{},r||!l.trigger||!1!==l.trigger.apply(n,t))){if(!r&&!l.noBubble&&!w.isWindow(n)){for(s=l.delegateType||f,U.test(s+f)||(d=d.parentNode);d;d=d.parentNode)c.push(d),o=d;o===(n.ownerDocument||v)&&c.push(o.defaultView||o.parentWindow||h)}for(i=0;(d=c[i++])&&!e.isPropagationStopped();)e.type=1<i?s:l.bindType||f,(u=(F.get(d,"events")||{})[e.type]&&F.get(d,"handle"))&&u.apply(d,t),(u=a&&d[a])&&u.apply&&w.acceptData(d)&&(e.result=u.apply(d,t),!1===e.result&&e.preventDefault());return e.type=f,r||e.isDefaultPrevented()||l._default&&!1!==l._default.apply(c.pop(),t)||!w.acceptData(n)||a&&w.isFunction(n[f])&&!w.isWindow(n)&&((o=n[a])&&(n[a]=null),n[w.event.triggered=f](),w.event.triggered=void 0,o&&(n[a]=o)),e.result}},dispatch:function(e){e=w.event.fix(e);var t,n,r,i,o,s=c.call(arguments),a=(F.get(this,"events")||{})[e.type]||[],u=w.event.special[e.type]||{};if((s[0]=e).delegateTarget=this,!u.preDispatch||!1!==u.preDispatch.call(this,e)){for(o=w.event.handlers.call(this,e,a),t=0;(r=o[t++])&&!e.isPropagationStopped();)for(e.currentTarget=r.elem,n=0;(i=r.handlers[n++])&&!e.isImmediatePropagationStopped();)e.namespace_re&&!e.namespace_re.test(i.namespace)||(e.handleObj=i,e.data=i.data,void 0!==(i=((w.event.special[i.origType]||{}).handle||i.handler).apply(r.elem,s))&&!1===(e.result=i)&&(e.preventDefault(),e.stopPropagation()));return u.postDispatch&&u.postDispatch.call(this,e),e.result}},handlers:function(e,t){var n,r,i,o,s=[],a=t.delegateCount,u=e.target;if(a&&u.nodeType&&(!e.button||"click"!==e.type))for(;u!==this;u=u.parentNode||this)if(!0!==u.disabled||"click"!==e.type){for(r=[],n=0;n<a;n++)void 0===r[i=(o=t[n]).selector+" "]&&(r[i]=o.needsContext?0<=w(i,this).index(u):w.find(i,this,null,[u]).length),r[i]&&r.push(o);r.length&&s.push({elem:u,handlers:r})}return a<t.length&&s.push({elem:this,handlers:t.slice(a)}),s},props:"altKey bubbles cancelable ctrlKey currentTarget eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(e,t){return null==e.which&&(e.which=null!=t.charCode?t.charCode:t.keyCode),e}},mouseHooks:{props:"button buttons clientX clientY offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(e,t){var n,r,i=t.button;return null==e.pageX&&null!=t.clientX&&(n=(r=e.target.ownerDocument||v).documentElement,r=r.body,e.pageX=t.clientX+(n&&n.scrollLeft||r&&r.scrollLeft||0)-(n&&n.clientLeft||r&&r.clientLeft||0),e.pageY=t.clientY+(n&&n.scrollTop||r&&r.scrollTop||0)-(n&&n.clientTop||r&&r.clientTop||0)),e.which||void 0===i||(e.which=1&i?1:2&i?3:4&i?2:0),e}},fix:function(e){if(e[w.expando])return e;var t,n,r,i=e.type,o=e,s=this.fixHooks[i];for(s||(this.fixHooks[i]=s=X.test(i)?this.mouseHooks:z.test(i)?this.keyHooks:{}),r=s.props?this.props.concat(s.props):this.props,e=new w.Event(o),t=r.length;t--;)e[n=r[t]]=o[n];return e.target||(e.target=v),3===e.target.nodeType&&(e.target=e.target.parentNode),s.filter?s.filter(e,o):e},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==Q()&&this.focus)return this.focus(),!1},delegateType:"focusin"},blur:{trigger:function(){if(this===Q()&&this.blur)return this.blur(),!1},delegateType:"focusout"},click:{trigger:function(){if("checkbox"===this.type&&this.click&&w.nodeName(this,"input"))return this.click(),!1},_default:function(e){return w.nodeName(e.target,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}},simulate:function(e,t,n,r){e=w.extend(new w.Event,n,{type:e,isSimulated:!0,originalEvent:{}});r?w.event.trigger(e,null,t):w.event.dispatch.call(t,e),e.isDefaultPrevented()&&n.preventDefault()}},w.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n,!1)},w.Event=function(e,t){if(!(this instanceof w.Event))return new w.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?Y:G):this.type=e,t&&w.extend(this,t),this.timeStamp=e&&e.timeStamp||w.now(),this[w.expando]=!0},w.Event.prototype={isDefaultPrevented:G,isPropagationStopped:G,isImmediatePropagationStopped:G,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=Y,e&&e.preventDefault&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=Y,e&&e.stopPropagation&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=Y,e&&e.stopImmediatePropagation&&e.stopImmediatePropagation(),this.stopPropagation()}},w.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,i){w.event.special[e]={delegateType:i,bindType:i,handle:function(e){var t,n=e.relatedTarget,r=e.handleObj;return n&&(n===this||w.contains(this,n))||(e.type=r.origType,t=r.handler.apply(this,arguments),e.type=i),t}}}),y.focusinBubbles||w.each({focus:"focusin",blur:"focusout"},function(n,r){function i(e){w.event.simulate(r,e.target,w.event.fix(e),!0)}w.event.special[r]={setup:function(){var e=this.ownerDocument||this,t=F.access(e,r);t||e.addEventListener(n,i,!0),F.access(e,r,(t||0)+1)},teardown:function(){var e=this.ownerDocument||this,t=F.access(e,r)-1;t?F.access(e,r,t):(e.removeEventListener(n,i,!0),F.remove(e,r))}}}),w.fn.extend({on:function(e,t,n,r,i){var o,s;if("object"==typeof e){for(s in"string"!=typeof t&&(n=n||t,t=void 0),e)this.on(s,t,n,e[s],i);return this}if(null==n&&null==r?(r=t,n=t=void 0):null==r&&("string"==typeof t?(r=n,n=void 0):(r=n,n=t,t=void 0)),!1===r)r=G;else if(!r)return this;return 1===i&&(o=r,(r=function(e){return w().off(e),o.apply(this,arguments)}).guid=o.guid||(o.guid=w.guid++)),this.each(function(){w.event.add(this,e,r,n,t)})},one:function(e,t,n,r){return this.on(e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,w(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"!=typeof e)return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=G),this.each(function(){w.event.remove(this,e,n,t)});for(i in e)this.off(i,t,e[i]);return this},trigger:function(e,t){return this.each(function(){w.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return w.event.trigger(e,t,n,!0)}});var J=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,K=/<([\w:]+)/,Z=/<|&#?\w+;/,ee=/<(?:script|style|link)/i,te=/checked\s*(?:[^=]|=\s*.checked.)/i,ne=/^$|\/(?:java|ecma)script/i,re=/^true\/(.*)/,ie=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,oe={option:[1,"<select multiple='multiple'>","</select>"],thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function se(e,t){return w.nodeName(e,"table")&&w.nodeName(11!==t.nodeType?t:t.firstChild,"tr")?e.getElementsByTagName("tbody")[0]||e.appendChild(e.ownerDocument.createElement("tbody")):e}function ae(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function ue(e){var t=re.exec(e.type);return t?e.type=t[1]:e.removeAttribute("type"),e}function le(e,t){for(var n=0,r=e.length;n<r;n++)F.set(e[n],"globalEval",!t||F.get(t[n],"globalEval"))}function ce(e,t){var n,r,i,o,s,a;if(1===t.nodeType){if(F.hasData(e)&&(o=F.access(e),s=F.set(t,o),a=o.events))for(i in delete s.handle,s.events={},a)for(n=0,r=a[i].length;n<r;n++)w.event.add(t,i,a[i][n]);P.hasData(e)&&(e=P.access(e),e=w.extend({},e),P.set(t,e))}}function fe(e,t){var n=e.getElementsByTagName?e.getElementsByTagName(t||"*"):e.querySelectorAll?e.querySelectorAll(t||"*"):[];return void 0===t||t&&w.nodeName(e,t)?w.merge([e],n):n}oe.optgroup=oe.option,oe.tbody=oe.tfoot=oe.colgroup=oe.caption=oe.thead,oe.th=oe.td,w.extend({clone:function(e,t,n){var r,i,o,s,a,u,l,c=e.cloneNode(!0),f=w.contains(e.ownerDocument,e);if(!(y.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||w.isXMLDoc(e)))for(s=fe(c),r=0,i=(o=fe(e)).length;r<i;r++)a=o[r],u=s[r],l=void 0,"input"===(l=u.nodeName.toLowerCase())&&I.test(a.type)?u.checked=a.checked:"input"!==l&&"textarea"!==l||(u.defaultValue=a.defaultValue);if(t)if(n)for(o=o||fe(e),s=s||fe(c),r=0,i=o.length;r<i;r++)ce(o[r],s[r]);else ce(e,c);return 0<(s=fe(c,"script")).length&&le(s,!f&&fe(e,"script")),c},buildFragment:function(e,t,n,r){for(var i,o,s,a,u,l=t.createDocumentFragment(),c=[],f=0,p=e.length;f<p;f++)if((i=e[f])||0===i)if("object"===w.type(i))w.merge(c,i.nodeType?[i]:i);else if(Z.test(i)){for(o=o||l.appendChild(t.createElement("div")),s=(K.exec(i)||["",""])[1].toLowerCase(),s=oe[s]||oe._default,o.innerHTML=s[1]+i.replace(J,"<$1></$2>")+s[2],u=s[0];u--;)o=o.lastChild;w.merge(c,o.childNodes),(o=l.firstChild).textContent=""}else c.push(t.createTextNode(i));for(l.textContent="",f=0;i=c[f++];)if((!r||-1===w.inArray(i,r))&&(a=w.contains(i.ownerDocument,i),o=fe(l.appendChild(i),"script"),a&&le(o),n))for(u=0;i=o[u++];)ne.test(i.type||"")&&n.push(i);return l},cleanData:function(e){for(var t,n,r,i,o=w.event.special,s=0;void 0!==(n=e[s]);s++){if(w.acceptData(n)&&(i=n[F.expando])&&(t=F.cache[i])){if(t.events)for(r in t.events)o[r]?w.event.remove(n,r):w.removeEvent(n,r,t.handle);F.cache[i]&&delete F.cache[i]}delete P.cache[n[P.expando]]}}}),w.fn.extend({text:function(e){return H(this,function(e){return void 0===e?w.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return this.domManip(arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||se(this,e).appendChild(e)})},prepend:function(){return this.domManip(arguments,function(e){var t;1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(t=se(this,e)).insertBefore(e,t.firstChild)})},before:function(){return this.domManip(arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return this.domManip(arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},remove:function(e,t){for(var n,r=e?w.filter(e,this):this,i=0;null!=(n=r[i]);i++)t||1!==n.nodeType||w.cleanData(fe(n)),n.parentNode&&(t&&w.contains(n.ownerDocument,n)&&le(fe(n,"script")),n.parentNode.removeChild(n));return this},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(w.cleanData(fe(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return w.clone(this,e,t)})},html:function(e){return H(this,function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!ee.test(e)&&!oe[(K.exec(e)||["",""])[1].toLowerCase()]){e=e.replace(J,"<$1></$2>");try{for(;n<r;n++)1===(t=this[n]||{}).nodeType&&(w.cleanData(fe(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var t=arguments[0];return this.domManip(arguments,function(e){t=this.parentNode,w.cleanData(fe(this)),t&&t.replaceChild(e,this)}),t&&(t.length||t.nodeType)?this:this.remove()},detach:function(e){return this.remove(e,!0)},domManip:function(n,r){n=g.apply([],n);var e,t,i,o,s,a,u=0,l=this.length,c=this,f=l-1,p=n[0],d=w.isFunction(p);if(d||1<l&&"string"==typeof p&&!y.checkClone&&te.test(p))return this.each(function(e){var t=c.eq(e);d&&(n[0]=p.call(this,e,t.html())),t.domManip(n,r)});if(l&&(t=(e=w.buildFragment(n,this[0].ownerDocument,!1,this)).firstChild,1===e.childNodes.length&&(e=t),t)){for(o=(i=w.map(fe(e,"script"),ae)).length;u<l;u++)s=e,u!==f&&(s=w.clone(s,!0,!0),o&&w.merge(i,fe(s,"script"))),r.call(this[u],s,u);if(o)for(a=i[i.length-1].ownerDocument,w.map(i,ue),u=0;u<o;u++)s=i[u],ne.test(s.type||"")&&!F.access(s,"globalEval")&&w.contains(a,s)&&(s.src?w._evalUrl&&w._evalUrl(s.src):w.globalEval(s.textContent.replace(ie,"")))}return this}}),w.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,s){w.fn[e]=function(e){for(var t,n=[],r=w(e),i=r.length-1,o=0;o<=i;o++)t=o===i?this:this.clone(!0),w(r[o])[s](t),a.apply(n,t.get());return this.pushStack(n)}});var pe,de={};function he(e,t){var t=w(t.createElement(e)).appendTo(t.body),n=h.getDefaultComputedStyle&&(n=h.getDefaultComputedStyle(t[0]))?n.display:w.css(t[0],"display");return t.detach(),n}function ge(e){var t=v,n=de[e];return n||("none"!==(n=he(e,t))&&n||((t=(pe=(pe||w("<iframe frameborder='0' width='0' height='0'/>")).appendTo(t.documentElement))[0].contentDocument).write(),t.close(),n=he(e,t),pe.detach()),de[e]=n),n}function me(e){return e.ownerDocument.defaultView.getComputedStyle(e,null)}var ye,ve,xe,be,we,Te=/^margin/,Ce=new RegExp("^("+n+")(?!px)[a-z%]+$","i");function Ne(e,t,n){var r,i,o=e.style;return(n=n||me(e))&&(i=n.getPropertyValue(t)||n[t]),n&&(""!==i||w.contains(e.ownerDocument,e)||(i=w.style(e,t)),Ce.test(i)&&Te.test(t)&&(r=o.width,e=o.minWidth,t=o.maxWidth,o.minWidth=o.maxWidth=o.width=i,i=n.width,o.width=r,o.minWidth=e,o.maxWidth=t)),void 0!==i?i+"":i}function ke(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}function Ee(){we.style.cssText="-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;display:block;margin-top:1%;top:1%;border:1px;padding:1px;width:4px;position:absolute",we.innerHTML="",xe.appendChild(be);var e=h.getComputedStyle(we,null);ye="1%"!==e.top,ve="4px"===e.width,xe.removeChild(be)}xe=v.documentElement,be=v.createElement("div"),(we=v.createElement("div")).style&&(we.style.backgroundClip="content-box",we.cloneNode(!0).style.backgroundClip="",y.clearCloneStyle="content-box"===we.style.backgroundClip,be.style.cssText="border:0;width:0;height:0;top:0;left:-9999px;margin-top:1px;position:absolute",be.appendChild(we),h.getComputedStyle&&w.extend(y,{pixelPosition:function(){return Ee(),ye},boxSizingReliable:function(){return null==ve&&Ee(),ve},reliableMarginRight:function(){var e=we.appendChild(v.createElement("div"));return e.style.cssText=we.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:0",e.style.marginRight=e.style.width="0",we.style.width="1px",xe.appendChild(be),e=!parseFloat(h.getComputedStyle(e,null).marginRight),xe.removeChild(be),e}})),w.swap=function(e,t,n,r){var i,o={};for(i in t)o[i]=e.style[i],e.style[i]=t[i];for(i in r=n.apply(e,r||[]),t)e.style[i]=o[i];return r};var Se=/^(none|table(?!-c[ea]).+)/,je=new RegExp("^("+n+")(.*)$","i"),De=new RegExp("^([+-])=("+n+")","i"),Ae={position:"absolute",visibility:"hidden",display:"block"},Le={letterSpacing:"0",fontWeight:"400"},qe=["Webkit","O","Moz","ms"];function He(e,t){if(t in e)return t;for(var n=t[0].toUpperCase()+t.slice(1),r=t,i=qe.length;i--;)if((t=qe[i]+n)in e)return t;return r}function Oe(e,t,n){var r=je.exec(t);return r?Math.max(0,r[1]-(n||0))+(r[2]||"px"):t}function Fe(e,t,n,r,i){for(var o=n===(r?"border":"content")?4:"width"===t?1:0,s=0;o<4;o+=2)"margin"===n&&(s+=w.css(e,n+B[o],!0,i)),r?("content"===n&&(s-=w.css(e,"padding"+B[o],!0,i)),"margin"!==n&&(s-=w.css(e,"border"+B[o]+"Width",!0,i))):(s+=w.css(e,"padding"+B[o],!0,i),"padding"!==n&&(s+=w.css(e,"border"+B[o]+"Width",!0,i)));return s}function Pe(e,t,n){var r=!0,i="width"===t?e.offsetWidth:e.offsetHeight,o=me(e),s="border-box"===w.css(e,"boxSizing",!1,o);if(i<=0||null==i){if(((i=Ne(e,t,o))<0||null==i)&&(i=e.style[t]),Ce.test(i))return i;r=s&&(y.boxSizingReliable()||i===e.style[t]),i=parseFloat(i)||0}return i+Fe(e,t,n||(s?"border":"content"),r,o)+"px"}function Me(e,t){for(var n,r,i,o=[],s=0,a=e.length;s<a;s++)(r=e[s]).style&&(o[s]=F.get(r,"olddisplay"),n=r.style.display,t?(o[s]||"none"!==n||(r.style.display=""),""===r.style.display&&$(r)&&(o[s]=F.access(r,"olddisplay",ge(r.nodeName)))):(i=$(r),"none"===n&&i||F.set(r,"olddisplay",i?n:w.css(r,"display"))));for(s=0;s<a;s++)(r=e[s]).style&&(t&&"none"!==r.style.display&&""!==r.style.display||(r.style.display=t?o[s]||"":"none"));return e}function Re(e,t,n,r,i){return new Re.prototype.init(e,t,n,r,i)}w.extend({cssHooks:{opacity:{get:function(e,t){if(t){e=Ne(e,"opacity");return""===e?"1":e}}}},cssNumber:{columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{float:"cssFloat"},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,o,s,a=w.camelCase(t),u=e.style;if(t=w.cssProps[a]||(w.cssProps[a]=He(u,a)),s=w.cssHooks[t]||w.cssHooks[a],void 0===n)return s&&"get"in s&&void 0!==(i=s.get(e,!1,r))?i:u[t];"string"===(o=typeof n)&&(i=De.exec(n))&&(n=(i[1]+1)*i[2]+parseFloat(w.css(e,t)),o="number"),null!=n&&n==n&&("number"!==o||w.cssNumber[a]||(n+="px"),y.clearCloneStyle||""!==n||0!==t.indexOf("background")||(u[t]="inherit"),s&&"set"in s&&void 0===(n=s.set(e,n,r))||(u[t]=n))}},css:function(e,t,n,r){var i,o=w.camelCase(t);return t=w.cssProps[o]||(w.cssProps[o]=He(e.style,o)),"normal"===(i=void 0===(i=(o=w.cssHooks[t]||w.cssHooks[o])&&"get"in o?o.get(e,!0,n):i)?Ne(e,t,r):i)&&t in Le&&(i=Le[t]),""===n||n?(t=parseFloat(i),!0===n||w.isNumeric(t)?t||0:i):i}}),w.each(["height","width"],function(e,i){w.cssHooks[i]={get:function(e,t,n){if(t)return Se.test(w.css(e,"display"))&&0===e.offsetWidth?w.swap(e,Ae,function(){return Pe(e,i,n)}):Pe(e,i,n)},set:function(e,t,n){var r=n&&me(e);return Oe(0,t,n?Fe(e,i,n,"border-box"===w.css(e,"boxSizing",!1,r),r):0)}}}),w.cssHooks.marginRight=ke(y.reliableMarginRight,function(e,t){if(t)return w.swap(e,{display:"inline-block"},Ne,[e,"marginRight"])}),w.each({margin:"",padding:"",border:"Width"},function(i,o){w.cssHooks[i+o]={expand:function(e){for(var t=0,n={},r="string"==typeof e?e.split(" "):[e];t<4;t++)n[i+B[t]+o]=r[t]||r[t-2]||r[0];return n}},Te.test(i)||(w.cssHooks[i+o].set=Oe)}),w.fn.extend({css:function(e,t){return H(this,function(e,t,n){var r,i,o={},s=0;if(w.isArray(t)){for(r=me(e),i=t.length;s<i;s++)o[t[s]]=w.css(e,t[s],!1,r);return o}return void 0!==n?w.style(e,t,n):w.css(e,t)},e,t,1<arguments.length)},show:function(){return Me(this,!0)},hide:function(){return Me(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){$(this)?w(this).show():w(this).hide()})}}),((w.Tween=Re).prototype={constructor:Re,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||"swing",this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(w.cssNumber[n]?"":"px")},cur:function(){var e=Re.propHooks[this.prop];return(e&&e.get?e:Re.propHooks._default).get(this)},run:function(e){var t,n=Re.propHooks[this.prop];return this.options.duration?this.pos=t=w.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),(n&&n.set?n:Re.propHooks._default).set(this),this}}).init.prototype=Re.prototype,(Re.propHooks={_default:{get:function(e){var t;return null==e.elem[e.prop]||e.elem.style&&null!=e.elem.style[e.prop]?(t=w.css(e.elem,e.prop,""))&&"auto"!==t?t:0:e.elem[e.prop]},set:function(e){w.fx.step[e.prop]?w.fx.step[e.prop](e):e.elem.style&&(null!=e.elem.style[w.cssProps[e.prop]]||w.cssHooks[e.prop])?w.style(e.elem,e.prop,e.now+e.unit):e.elem[e.prop]=e.now}}}).scrollTop=Re.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},w.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2}},w.fx=Re.prototype.init,w.fx.step={};var We,$e,Be=/^(?:toggle|show|hide)$/,Ie=new RegExp("^(?:([+-])=|)("+n+")([a-z%]*)$","i"),_e=/queueHooks$/,ze=[function(t,e,n){var r,i,o,s,a,u,l,c=this,f={},p=t.style,d=t.nodeType&&$(t),h=F.get(t,"fxshow");n.queue||(null==(a=w._queueHooks(t,"fx")).unqueued&&(a.unqueued=0,u=a.empty.fire,a.empty.fire=function(){a.unqueued||u()}),a.unqueued++,c.always(function(){c.always(function(){a.unqueued--,w.queue(t,"fx").length||a.empty.fire()})}));1===t.nodeType&&("height"in e||"width"in e)&&(n.overflow=[p.overflow,p.overflowX,p.overflowY],"inline"===("none"===(l=w.css(t,"display"))?ge(t.nodeName):l)&&"none"===w.css(t,"float")&&(p.display="inline-block"));n.overflow&&(p.overflow="hidden",c.always(function(){p.overflow=n.overflow[0],p.overflowX=n.overflow[1],p.overflowY=n.overflow[2]}));for(r in e)if(i=e[r],Be.exec(i)){if(delete e[r],o=o||"toggle"===i,i===(d?"hide":"show")){if("show"!==i||!h||void 0===h[r])continue;d=!0}f[r]=h&&h[r]||w.style(t,r)}else l=void 0;if(w.isEmptyObject(f))"inline"===("none"===l?ge(t.nodeName):l)&&(p.display=l);else for(r in h?"hidden"in h&&(d=h.hidden):h=F.access(t,"fxshow",{}),o&&(h.hidden=!d),d?w(t).show():c.done(function(){w(t).hide()}),c.done(function(){for(var e in F.remove(t,"fxshow"),f)w.style(t,e,f[e])}),f)s=Ye(d?h[r]:0,r,c),r in h||(h[r]=s.start,d&&(s.end=s.start,s.start="width"===r||"height"===r?1:0))}],Xe={"*":[function(e,t){var n=this.createTween(e,t),r=n.cur(),t=Ie.exec(t),i=t&&t[3]||(w.cssNumber[e]?"":"px"),o=(w.cssNumber[e]||"px"!==i&&+r)&&Ie.exec(w.css(n.elem,e)),s=1,a=20;if(o&&o[3]!==i)for(i=i||o[3],t=t||[],o=+r||1;o/=s=s||".5",w.style(n.elem,e,o+i),s!==(s=n.cur()/r)&&1!==s&&--a;);return t&&(o=n.start=+o||+r||0,n.unit=i,n.end=t[1]?o+(t[1]+1)*t[2]:+t[2]),n}]};function Ue(){return setTimeout(function(){We=void 0}),We=w.now()}function Ve(e,t){var n,r=0,i={height:e};for(t=t?1:0;r<4;r+=2-t)i["margin"+(n=B[r])]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function Ye(e,t,n){for(var r,i=(Xe[t]||[]).concat(Xe["*"]),o=0,s=i.length;o<s;o++)if(r=i[o].call(n,t,e))return r}function Ge(i,e,t){var n,o,r=0,s=ze.length,a=w.Deferred().always(function(){delete u.elem}),u=function(){if(o)return!1;for(var e=We||Ue(),e=Math.max(0,l.startTime+l.duration-e),t=1-(e/l.duration||0),n=0,r=l.tweens.length;n<r;n++)l.tweens[n].run(t);return a.notifyWith(i,[l,t,e]),t<1&&r?e:(a.resolveWith(i,[l]),!1)},l=a.promise({elem:i,props:w.extend({},e),opts:w.extend(!0,{specialEasing:{}},t),originalProperties:e,originalOptions:t,startTime:We||Ue(),duration:t.duration,tweens:[],createTween:function(e,t){e=w.Tween(i,l.opts,e,t,l.opts.specialEasing[e]||l.opts.easing);return l.tweens.push(e),e},stop:function(e){var t=0,n=e?l.tweens.length:0;if(o)return this;for(o=!0;t<n;t++)l.tweens[t].run(1);return e?a.resolveWith(i,[l,e]):a.rejectWith(i,[l,e]),this}}),c=l.props;for(!function(e,t){var n,r,i,o,s;for(n in e)if(i=t[r=w.camelCase(n)],o=e[n],w.isArray(o)&&(i=o[1],o=e[n]=o[0]),n!==r&&(e[r]=o,delete e[n]),(s=w.cssHooks[r])&&"expand"in s)for(n in o=s.expand(o),delete e[r],o)n in e||(e[n]=o[n],t[n]=i);else t[r]=i}(c,l.opts.specialEasing);r<s;r++)if(n=ze[r].call(l,i,c,l.opts))return n;return w.map(c,Ye,l),w.isFunction(l.opts.start)&&l.opts.start.call(i,l),w.fx.timer(w.extend(u,{elem:i,anim:l,queue:l.opts.queue})),l.progress(l.opts.progress).done(l.opts.done,l.opts.complete).fail(l.opts.fail).always(l.opts.always)}w.Animation=w.extend(Ge,{tweener:function(e,t){for(var n,r=0,i=(e=w.isFunction(e)?(t=e,["*"]):e.split(" ")).length;r<i;r++)n=e[r],Xe[n]=Xe[n]||[],Xe[n].unshift(t)},prefilter:function(e,t){t?ze.unshift(e):ze.push(e)}}),w.speed=function(e,t,n){var r=e&&"object"==typeof e?w.extend({},e):{complete:n||!n&&t||w.isFunction(e)&&e,duration:e,easing:n&&t||t&&!w.isFunction(t)&&t};return r.duration=w.fx.off?0:"number"==typeof r.duration?r.duration:r.duration in w.fx.speeds?w.fx.speeds[r.duration]:w.fx.speeds._default,null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){w.isFunction(r.old)&&r.old.call(this),r.queue&&w.dequeue(this,r.queue)},r},w.fn.extend({fadeTo:function(e,t,n,r){return this.filter($).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(t,e,n,r){var i=w.isEmptyObject(t),o=w.speed(e,n,r),r=function(){var e=Ge(this,w.extend({},t),o);(i||F.get(this,"finish"))&&e.stop(!0)};return r.finish=r,i||!1===o.queue?this.each(r):this.queue(o.queue,r)},stop:function(i,e,o){function s(e){var t=e.stop;delete e.stop,t(o)}return"string"!=typeof i&&(o=e,e=i,i=void 0),e&&!1!==i&&this.queue(i||"fx",[]),this.each(function(){var e=!0,t=null!=i&&i+"queueHooks",n=w.timers,r=F.get(this);if(t)r[t]&&r[t].stop&&s(r[t]);else for(t in r)r[t]&&r[t].stop&&_e.test(t)&&s(r[t]);for(t=n.length;t--;)n[t].elem!==this||null!=i&&n[t].queue!==i||(n[t].anim.stop(o),e=!1,n.splice(t,1));!e&&o||w.dequeue(this,i)})},finish:function(s){return!1!==s&&(s=s||"fx"),this.each(function(){var e,t=F.get(this),n=t[s+"queue"],r=t[s+"queueHooks"],i=w.timers,o=n?n.length:0;for(t.finish=!0,w.queue(this,s,[]),r&&r.stop&&r.stop.call(this,!0),e=i.length;e--;)i[e].elem===this&&i[e].queue===s&&(i[e].anim.stop(!0),i.splice(e,1));for(e=0;e<o;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete t.finish})}}),w.each(["toggle","show","hide"],function(e,r){var i=w.fn[r];w.fn[r]=function(e,t,n){return null==e||"boolean"==typeof e?i.apply(this,arguments):this.animate(Ve(r,!0),e,t,n)}}),w.each({slideDown:Ve("show"),slideUp:Ve("hide"),slideToggle:Ve("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,r){w.fn[e]=function(e,t,n){return this.animate(r,e,t,n)}}),w.timers=[],w.fx.tick=function(){var e,t=0,n=w.timers;for(We=w.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||w.fx.stop(),We=void 0},w.fx.timer=function(e){w.timers.push(e),e()?w.fx.start():w.timers.pop()},w.fx.interval=13,w.fx.start=function(){$e=$e||setInterval(w.fx.tick,w.fx.interval)},w.fx.stop=function(){clearInterval($e),$e=null},w.fx.speeds={slow:600,fast:200,_default:400},w.fn.delay=function(r,e){return r=w.fx&&w.fx.speeds[r]||r,this.queue(e=e||"fx",function(e,t){var n=setTimeout(e,r);t.stop=function(){clearTimeout(n)}})},d=v.createElement("input"),s=v.createElement("select"),n=s.appendChild(v.createElement("option")),d.type="checkbox",y.checkOn=""!==d.value,y.optSelected=n.selected,s.disabled=!0,y.optDisabled=!n.disabled,(d=v.createElement("input")).value="t",d.type="radio",y.radioValue="t"===d.value;var Qe,Je=w.expr.attrHandle;w.fn.extend({attr:function(e,t){return H(this,w.attr,e,t,1<arguments.length)},removeAttr:function(e){return this.each(function(){w.removeAttr(this,e)})}}),w.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(e&&3!==o&&8!==o&&2!==o)return typeof e.getAttribute==_?w.prop(e,t,n):(1===o&&w.isXMLDoc(e)||(t=t.toLowerCase(),r=w.attrHooks[t]||(w.expr.match.bool.test(t)?Qe:void 0)),void 0===n?!(r&&"get"in r&&null!==(i=r.get(e,t)))&&null==(i=w.find.attr(e,t))?void 0:i:null!==n?r&&"set"in r&&void 0!==(i=r.set(e,n,t))?i:(e.setAttribute(t,n+""),n):void w.removeAttr(e,t))},removeAttr:function(e,t){var n,r,i=0,o=t&&t.match(A);if(o&&1===e.nodeType)for(;n=o[i++];)r=w.propFix[n]||n,w.expr.match.bool.test(n)&&(e[r]=!1),e.removeAttribute(n)},attrHooks:{type:{set:function(e,t){if(!y.radioValue&&"radio"===t&&w.nodeName(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}}}),Qe={set:function(e,t,n){return!1===t?w.removeAttr(e,n):e.setAttribute(n,n),n}},w.each(w.expr.match.bool.source.match(/\w+/g),function(e,t){var o=Je[t]||w.find.attr;Je[t]=function(e,t,n){var r,i;return n||(i=Je[t],Je[t]=r,r=null!=o(e,t,n)?t.toLowerCase():null,Je[t]=i),r}});var Ke=/^(?:input|select|textarea|button)$/i;w.fn.extend({prop:function(e,t){return H(this,w.prop,e,t,1<arguments.length)},removeProp:function(e){return this.each(function(){delete this[w.propFix[e]||e]})}}),w.extend({propFix:{for:"htmlFor",class:"className"},prop:function(e,t,n){var r,i,o=e.nodeType;if(e&&3!==o&&8!==o&&2!==o)return(1!==o||!w.isXMLDoc(e))&&(t=w.propFix[t]||t,i=w.propHooks[t]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){return e.hasAttribute("tabindex")||Ke.test(e.nodeName)||e.href?e.tabIndex:-1}}}}),y.optSelected||(w.propHooks.selected={get:function(e){e=e.parentNode;return e&&e.parentNode&&e.parentNode.selectedIndex,null}}),w.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){w.propFix[this.toLowerCase()]=this});var Ze=/[\t\r\n\f]/g;w.fn.extend({addClass:function(t){var e,n,r,i,o,s,a="string"==typeof t&&t,u=0,l=this.length;if(w.isFunction(t))return this.each(function(e){w(this).addClass(t.call(this,e,this.className))});if(a)for(e=(t||"").match(A)||[];u<l;u++)if(r=1===(n=this[u]).nodeType&&(n.className?(" "+n.className+" ").replace(Ze," "):" ")){for(o=0;i=e[o++];)r.indexOf(" "+i+" ")<0&&(r+=i+" ");s=w.trim(r),n.className!==s&&(n.className=s)}return this},removeClass:function(t){var e,n,r,i,o,s,a=0===arguments.length||"string"==typeof t&&t,u=0,l=this.length;if(w.isFunction(t))return this.each(function(e){w(this).removeClass(t.call(this,e,this.className))});if(a)for(e=(t||"").match(A)||[];u<l;u++)if(r=1===(n=this[u]).nodeType&&(n.className?(" "+n.className+" ").replace(Ze," "):"")){for(o=0;i=e[o++];)for(;0<=r.indexOf(" "+i+" ");)r=r.replace(" "+i+" "," ");s=t?w.trim(r):"",n.className!==s&&(n.className=s)}return this},toggleClass:function(i,t){var o=typeof i;return"boolean"==typeof t&&"string"==o?t?this.addClass(i):this.removeClass(i):w.isFunction(i)?this.each(function(e){w(this).toggleClass(i.call(this,e,this.className,t),t)}):this.each(function(){if("string"==o)for(var e,t=0,n=w(this),r=i.match(A)||[];e=r[t++];)n.hasClass(e)?n.removeClass(e):n.addClass(e);else o!=_&&"boolean"!=o||(this.className&&F.set(this,"__className__",this.className),this.className=!this.className&&!1!==i&&F.get(this,"__className__")||"")})},hasClass:function(e){for(var t=" "+e+" ",n=0,r=this.length;n<r;n++)if(1===this[n].nodeType&&0<=(" "+this[n].className+" ").replace(Ze," ").indexOf(t))return!0;return!1}});var et=/\r/g;w.fn.extend({val:function(t){var n,e,r,i=this[0];return arguments.length?(r=w.isFunction(t),this.each(function(e){1===this.nodeType&&(null==(e=r?t.call(this,e,w(this).val()):t)?e="":"number"==typeof e?e+="":w.isArray(e)&&(e=w.map(e,function(e){return null==e?"":e+""})),(n=w.valHooks[this.type]||w.valHooks[this.nodeName.toLowerCase()])&&"set"in n&&void 0!==n.set(this,e,"value")||(this.value=e))})):i?(n=w.valHooks[i.type]||w.valHooks[i.nodeName.toLowerCase()])&&"get"in n&&void 0!==(e=n.get(i,"value"))?e:"string"==typeof(e=i.value)?e.replace(et,""):null==e?"":e:void 0}}),w.extend({valHooks:{option:{get:function(e){var t=w.find.attr(e,"value");return null!=t?t:w.trim(w.text(e))}},select:{get:function(e){for(var t,n=e.options,r=e.selectedIndex,i="select-one"===e.type||r<0,o=i?null:[],s=i?r+1:n.length,a=r<0?s:i?r:0;a<s;a++)if(((t=n[a]).selected||a===r)&&(y.optDisabled?!t.disabled:null===t.getAttribute("disabled"))&&(!t.parentNode.disabled||!w.nodeName(t.parentNode,"optgroup"))){if(t=w(t).val(),i)return t;o.push(t)}return o},set:function(e,t){for(var n,r,i=e.options,o=w.makeArray(t),s=i.length;s--;)((r=i[s]).selected=0<=w.inArray(r.value,o))&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),w.each(["radio","checkbox"],function(){w.valHooks[this]={set:function(e,t){if(w.isArray(t))return e.checked=0<=w.inArray(w(e).val(),t)}},y.checkOn||(w.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}),w.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(e,n){w.fn[n]=function(e,t){return 0<arguments.length?this.on(n,null,e,t):this.trigger(n)}}),w.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)},bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)}});var tt=w.now(),nt=/\?/;w.parseJSON=function(e){return JSON.parse(e+"")},w.parseXML=function(e){var t;if(!e||"string"!=typeof e)return null;try{t=(new DOMParser).parseFromString(e,"text/xml")}catch(e){t=void 0}return t&&!t.getElementsByTagName("parsererror").length||w.error("Invalid XML: "+e),t};var rt,it,ot=/#.*$/,st=/([?&])_=[^&]*/,at=/^(.*?):[ \t]*([^\r\n]*)$/gm,ut=/^(?:GET|HEAD)$/,lt=/^\/\//,ct=/^([\w.+-]+:)(?:\/\/(?:[^\/?#]*@|)([^\/?#:]*)(?::(\d+)|)|)/,ft={},pt={},dt="*/".concat("*");try{it=location.href}catch(e){(it=v.createElement("a")).href="",it=it.href}function ht(o){return function(e,t){"string"!=typeof e&&(t=e,e="*");var n,r=0,i=e.toLowerCase().match(A)||[];if(w.isFunction(t))for(;n=i[r++];)"+"===n[0]?(n=n.slice(1)||"*",(o[n]=o[n]||[]).unshift(t)):(o[n]=o[n]||[]).push(t)}}function gt(t,r,i,o){var s={},a=t===pt;function u(e){var n;return s[e]=!0,w.each(t[e]||[],function(e,t){t=t(r,i,o);return"string"!=typeof t||a||s[t]?a?!(n=t):void 0:(r.dataTypes.unshift(t),u(t),!1)}),n}return u(r.dataTypes[0])||!s["*"]&&u("*")}function mt(e,t){var n,r,i=w.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((i[n]?e:r=r||{})[n]=t[n]);return r&&w.extend(!0,e,r),e}rt=ct.exec(it.toLowerCase())||[],w.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:it,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(rt[1]),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":dt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/xml/,html:/html/,json:/json/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":w.parseJSON,"text xml":w.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?mt(mt(e,w.ajaxSettings),t):mt(w.ajaxSettings,e)},ajaxPrefilter:ht(ft),ajaxTransport:ht(pt),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0);var u,l,c,n,f,p,r,d=w.ajaxSetup({},t=t||{}),h=d.context||d,g=d.context&&(h.nodeType||h.jquery)?w(h):w.event,m=w.Deferred(),y=w.Callbacks("once memory"),v=d.statusCode||{},i={},o={},x=0,s="canceled",b={readyState:0,getResponseHeader:function(e){var t;if(2===x){if(!n)for(n={};t=at.exec(c);)n[t[1].toLowerCase()]=t[2];t=n[e.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return 2===x?c:null},setRequestHeader:function(e,t){var n=e.toLowerCase();return x||(e=o[n]=o[n]||e,i[e]=t),this},overrideMimeType:function(e){return x||(d.mimeType=e),this},statusCode:function(e){if(e)if(x<2)for(var t in e)v[t]=[v[t],e[t]];else b.always(e[b.status]);return this},abort:function(e){e=e||s;return u&&u.abort(e),a(0,e),this}};if(m.promise(b).complete=y.add,b.success=b.done,b.error=b.fail,d.url=((e||d.url||it)+"").replace(ot,"").replace(lt,rt[1]+"//"),d.type=t.method||t.type||d.method||d.type,d.dataTypes=w.trim(d.dataType||"*").toLowerCase().match(A)||[""],null==d.crossDomain&&(e=ct.exec(d.url.toLowerCase()),d.crossDomain=!(!e||e[1]===rt[1]&&e[2]===rt[2]&&(e[3]||("http:"===e[1]?"80":"443"))===(rt[3]||("http:"===rt[1]?"80":"443")))),d.data&&d.processData&&"string"!=typeof d.data&&(d.data=w.param(d.data,d.traditional)),gt(ft,d,t,b),2===x)return b;for(r in(p=d.global)&&0==w.active++&&w.event.trigger("ajaxStart"),d.type=d.type.toUpperCase(),d.hasContent=!ut.test(d.type),l=d.url,d.hasContent||(d.data&&(l=d.url+=(nt.test(l)?"&":"?")+d.data,delete d.data),!1===d.cache&&(d.url=st.test(l)?l.replace(st,"$1_="+tt++):l+(nt.test(l)?"&":"?")+"_="+tt++)),d.ifModified&&(w.lastModified[l]&&b.setRequestHeader("If-Modified-Since",w.lastModified[l]),w.etag[l]&&b.setRequestHeader("If-None-Match",w.etag[l])),(d.data&&d.hasContent&&!1!==d.contentType||t.contentType)&&b.setRequestHeader("Content-Type",d.contentType),b.setRequestHeader("Accept",d.dataTypes[0]&&d.accepts[d.dataTypes[0]]?d.accepts[d.dataTypes[0]]+("*"!==d.dataTypes[0]?", "+dt+"; q=0.01":""):d.accepts["*"]),d.headers)b.setRequestHeader(r,d.headers[r]);if(d.beforeSend&&(!1===d.beforeSend.call(h,b,d)||2===x))return b.abort();for(r in s="abort",{success:1,error:1,complete:1})b[r](d[r]);if(u=gt(pt,d,t,b)){b.readyState=1,p&&g.trigger("ajaxSend",[b,d]),d.async&&0<d.timeout&&(f=setTimeout(function(){b.abort("timeout")},d.timeout));try{x=1,u.send(i,a)}catch(e){if(!(x<2))throw e;a(-1,e)}}else a(-1,"No Transport");function a(e,t,n,r){var i,o,s,a=t;2!==x&&(x=2,f&&clearTimeout(f),u=void 0,c=r||"",b.readyState=0<e?4:0,r=200<=e&&e<300||304===e,n&&(s=function(e,t,n){for(var r,i,o,s,a=e.contents,u=e.dataTypes;"*"===u[0];)u.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(i in a)if(a[i]&&a[i].test(r)){u.unshift(i);break}if(u[0]in n)o=u[0];else{for(i in n){if(!u[0]||e.converters[i+" "+u[0]]){o=i;break}s=s||i}o=o||s}if(o)return o!==u[0]&&u.unshift(o),n[o]}(d,b,n)),s=function(e,t,n,r){var i,o,s,a,u,l={},c=e.dataTypes.slice();if(c[1])for(s in e.converters)l[s.toLowerCase()]=e.converters[s];for(o=c.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!u&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),u=o,o=c.shift())if("*"===o)o=u;else if("*"!==u&&u!==o){if(!(s=l[u+" "+o]||l["* "+o]))for(i in l)if((a=i.split(" "))[1]===o&&(s=l[u+" "+a[0]]||l["* "+a[0]])){!0===s?s=l[i]:!0!==l[i]&&(o=a[0],c.unshift(a[1]));break}if(!0!==s)if(s&&e.throws)t=s(t);else try{t=s(t)}catch(e){return{state:"parsererror",error:s?e:"No conversion from "+u+" to "+o}}}return{state:"success",data:t}}(d,s,b,r),r?(d.ifModified&&((n=b.getResponseHeader("Last-Modified"))&&(w.lastModified[l]=n),(n=b.getResponseHeader("etag"))&&(w.etag[l]=n)),204===e||"HEAD"===d.type?a="nocontent":304===e?a="notmodified":(a=s.state,i=s.data,r=!(o=s.error))):(o=a,!e&&a||(a="error",e<0&&(e=0))),b.status=e,b.statusText=(t||a)+"",r?m.resolveWith(h,[i,a,b]):m.rejectWith(h,[b,a,o]),b.statusCode(v),v=void 0,p&&g.trigger(r?"ajaxSuccess":"ajaxError",[b,d,r?i:o]),y.fireWith(h,[b,a]),p&&(g.trigger("ajaxComplete",[b,d]),--w.active||w.event.trigger("ajaxStop")))}return b},getJSON:function(e,t,n){return w.get(e,t,n,"json")},getScript:function(e,t){return w.get(e,void 0,t,"script")}}),w.each(["get","post"],function(e,i){w[i]=function(e,t,n,r){return w.isFunction(t)&&(r=r||n,n=t,t=void 0),w.ajax({url:e,type:i,dataType:r,data:t,success:n})}}),w.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){w.fn[t]=function(e){return this.on(t,e)}}),w._evalUrl=function(e){return w.ajax({url:e,type:"GET",dataType:"script",async:!1,global:!1,throws:!0})},w.fn.extend({wrapAll:function(t){var e;return w.isFunction(t)?this.each(function(e){w(this).wrapAll(t.call(this,e))}):(this[0]&&(e=w(t,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this)},wrapInner:function(n){return w.isFunction(n)?this.each(function(e){w(this).wrapInner(n.call(this,e))}):this.each(function(){var e=w(this),t=e.contents();t.length?t.wrapAll(n):e.append(n)})},wrap:function(t){var n=w.isFunction(t);return this.each(function(e){w(this).wrapAll(n?t.call(this,e):t)})},unwrap:function(){return this.parent().each(function(){w.nodeName(this,"body")||w(this).replaceWith(this.childNodes)}).end()}}),w.expr.filters.hidden=function(e){return e.offsetWidth<=0&&e.offsetHeight<=0},w.expr.filters.visible=function(e){return!w.expr.filters.hidden(e)};var yt=/%20/g,vt=/\[\]$/,xt=/\r?\n/g,bt=/^(?:submit|button|image|reset|file)$/i,wt=/^(?:input|select|textarea|keygen)/i;w.param=function(e,t){function n(e,t){t=w.isFunction(t)?t():null==t?"":t,i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)}var r,i=[];if(void 0===t&&(t=w.ajaxSettings&&w.ajaxSettings.traditional),w.isArray(e)||e.jquery&&!w.isPlainObject(e))w.each(e,function(){n(this.name,this.value)});else for(r in e)!function n(r,e,i,o){if(w.isArray(e))w.each(e,function(e,t){i||vt.test(r)?o(r,t):n(r+"["+("object"==typeof t?e:"")+"]",t,i,o)});else if(i||"object"!==w.type(e))o(r,e);else for(var t in e)n(r+"["+t+"]",e[t],i,o)}(r,e[r],t,n);return i.join("&").replace(yt,"+")},w.fn.extend({serialize:function(){return w.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=w.prop(this,"elements");return e?w.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!w(this).is(":disabled")&&wt.test(this.nodeName)&&!bt.test(e)&&(this.checked||!I.test(e))}).map(function(e,t){var n=w(this).val();return null==n?null:w.isArray(n)?w.map(n,function(e){return{name:t.name,value:e.replace(xt,"\r\n")}}):{name:t.name,value:n.replace(xt,"\r\n")}}).get()}}),w.ajaxSettings.xhr=function(){try{return new XMLHttpRequest}catch(e){}};var Tt=0,Ct={},Nt={0:200,1223:204},kt=w.ajaxSettings.xhr();h.ActiveXObject&&w(h).on("unload",function(){for(var e in Ct)Ct[e]()}),y.cors=!!kt&&"withCredentials"in kt,y.ajax=kt=!!kt,w.ajaxTransport(function(o){var s;if(y.cors||kt&&!o.crossDomain)return{send:function(e,t){var n,r=o.xhr(),i=++Tt;if(r.open(o.type,o.url,o.async,o.username,o.password),o.xhrFields)for(n in o.xhrFields)r[n]=o.xhrFields[n];for(n in o.mimeType&&r.overrideMimeType&&r.overrideMimeType(o.mimeType),o.crossDomain||e["X-Requested-With"]||(e["X-Requested-With"]="XMLHttpRequest"),e)r.setRequestHeader(n,e[n]);s=function(e){return function(){s&&(delete Ct[i],s=r.onload=r.onerror=null,"abort"===e?r.abort():"error"===e?t(r.status,r.statusText):t(Nt[r.status]||r.status,r.statusText,"string"==typeof r.responseText?{text:r.responseText}:void 0,r.getAllResponseHeaders()))}},r.onload=s(),r.onerror=s("error"),s=Ct[i]=s("abort");try{r.send(o.hasContent&&o.data||null)}catch(e){if(s)throw e}},abort:function(){s&&s()}}}),w.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/(?:java|ecma)script/},converters:{"text script":function(e){return w.globalEval(e),e}}}),w.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),w.ajaxTransport("script",function(n){var r,i;if(n.crossDomain)return{send:function(e,t){r=w("<script>").prop({async:!0,charset:n.scriptCharset,src:n.url}).on("load error",i=function(e){r.remove(),i=null,e&&t("error"===e.type?404:200,e.type)}),v.head.appendChild(r[0])},abort:function(){i&&i()}}});var Et=[],St=/(=)\?(?=&|$)|\?\?/;w.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Et.pop()||w.expando+"_"+tt++;return this[e]=!0,e}}),w.ajaxPrefilter("json jsonp",function(e,t,n){var r,i,o,s=!1!==e.jsonp&&(St.test(e.url)?"url":"string"==typeof e.data&&!(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&St.test(e.data)&&"data");if(s||"jsonp"===e.dataTypes[0])return r=e.jsonpCallback=w.isFunction(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,s?e[s]=e[s].replace(St,"$1"+r):!1!==e.jsonp&&(e.url+=(nt.test(e.url)?"&":"?")+e.jsonp+"="+r),e.converters["script json"]=function(){return o||w.error(r+" was not called"),o[0]},e.dataTypes[0]="json",i=h[r],h[r]=function(){o=arguments},n.always(function(){h[r]=i,e[r]&&(e.jsonpCallback=t.jsonpCallback,Et.push(r)),o&&w.isFunction(i)&&i(o[0]),o=i=void 0}),"script"}),w.parseHTML=function(e,t,n){if(!e||"string"!=typeof e)return null;"boolean"==typeof t&&(n=t,t=!1),t=t||v;var r=b.exec(e),n=!n&&[];return r?[t.createElement(r[1])]:(r=w.buildFragment([e],t,n),n&&n.length&&w(n).remove(),w.merge([],r.childNodes))};var jt=w.fn.load;w.fn.load=function(e,t,n){if("string"!=typeof e&&jt)return jt.apply(this,arguments);var r,i,o,s=this,a=e.indexOf(" ");return 0<=a&&(r=w.trim(e.slice(a)),e=e.slice(0,a)),w.isFunction(t)?(n=t,t=void 0):t&&"object"==typeof t&&(i="POST"),0<s.length&&w.ajax({url:e,type:i,dataType:"html",data:t}).done(function(e){o=arguments,s.html(r?w("<div>").append(w.parseHTML(e)).find(r):e)}).complete(n&&function(e,t){s.each(n,o||[e.responseText,t,e])}),this},w.expr.filters.animated=function(t){return w.grep(w.timers,function(e){return t===e.elem}).length};var Dt=h.document.documentElement;function At(e){return w.isWindow(e)?e:9===e.nodeType&&e.defaultView}w.offset={setOffset:function(e,t,n){var r,i,o,s,a=w.css(e,"position"),u=w(e),l={};"static"===a&&(e.style.position="relative"),o=u.offset(),r=w.css(e,"top"),s=w.css(e,"left"),s=("absolute"===a||"fixed"===a)&&-1<(r+s).indexOf("auto")?(i=(a=u.position()).top,a.left):(i=parseFloat(r)||0,parseFloat(s)||0),null!=(t=w.isFunction(t)?t.call(e,n,o):t).top&&(l.top=t.top-o.top+i),null!=t.left&&(l.left=t.left-o.left+s),"using"in t?t.using.call(e,l):u.css(l)}},w.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each(function(e){w.offset.setOffset(this,t,e)});var e,n=this[0],r={top:0,left:0},i=n&&n.ownerDocument;return i?(e=i.documentElement,w.contains(e,n)?(typeof n.getBoundingClientRect!=_&&(r=n.getBoundingClientRect()),i=At(i),{top:r.top+i.pageYOffset-e.clientTop,left:r.left+i.pageXOffset-e.clientLeft}):r):void 0},position:function(){if(this[0]){var e,t,n=this[0],r={top:0,left:0};return"fixed"===w.css(n,"position")?t=n.getBoundingClientRect():(e=this.offsetParent(),t=this.offset(),(r=!w.nodeName(e[0],"html")?e.offset():r).top+=w.css(e[0],"borderTopWidth",!0),r.left+=w.css(e[0],"borderLeftWidth",!0)),{top:t.top-r.top-w.css(n,"marginTop",!0),left:t.left-r.left-w.css(n,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent||Dt;e&&!w.nodeName(e,"html")&&"static"===w.css(e,"position");)e=e.offsetParent;return e||Dt})}}),w.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,i){var o="pageYOffset"===i;w.fn[t]=function(e){return H(this,function(e,t,n){var r=At(e);if(void 0===n)return r?r[i]:e[t];r?r.scrollTo(o?h.pageXOffset:n,o?n:h.pageYOffset):e[t]=n},t,e,arguments.length,null)}}),w.each(["top","left"],function(e,n){w.cssHooks[n]=ke(y.pixelPosition,function(e,t){if(t)return t=Ne(e,n),Ce.test(t)?w(e).position()[n]+"px":t})}),w.each({Height:"height",Width:"width"},function(o,s){w.each({padding:"inner"+o,content:s,"":"outer"+o},function(r,e){w.fn[e]=function(e,t){var n=arguments.length&&(r||"boolean"!=typeof e),i=r||(!0===e||!0===t?"margin":"border");return H(this,function(e,t,n){var r;return w.isWindow(e)?e.document.documentElement["client"+o]:9===e.nodeType?(r=e.documentElement,Math.max(e.body["scroll"+o],r["scroll"+o],e.body["offset"+o],r["offset"+o],r["client"+o])):void 0===n?w.css(e,t,i):w.style(e,t,n,i)},s,n?e:void 0,n,null)}})}),w.fn.size=function(){return this.length},w.fn.andSelf=w.fn.addBack,"function"==typeof define&&define.amd&&define("jquery",[],function(){return w});var Lt=h.jQuery,qt=h.$;return w.noConflict=function(e){return h.$===w&&(h.$=qt),e&&h.jQuery===w&&(h.jQuery=Lt),w},typeof e==_&&(h.jQuery=h.$=w),w});