!function(I){var o={method:"GET",contentType:"json",queryParam:"q",searchDelay:300,minChars:1,propertyToSearch:"name",jsonContainer:null,hintText:"Type in a search term",noResultsText:"No results",searchingText:"Searching...",deleteText:"&times;",animateDropdown:!0,tokenLimit:null,tokenDelimiter:",",preventDuplicates:!1,tokenValue:"id",prePopulate:null,processPrePopulate:!1,idPrefix:"token-input-",resultsFormatter:function(e){return"<li>"+e[this.propertyToSearch]+"</li>"},tokenFormatter:function(e){return"<li><p>"+e[this.propertyToSearch]+"</p></li>"},onResult:null,onAdd:null,onDelete:null,onReady:null},F={tokenList:"token-input-list",token:"token-input-token",tokenDelete:"token-input-delete-token",selectedToken:"token-input-selected-token",highlightedToken:"token-input-highlighted-token",dropdown:"token-input-dropdown",dropdownItem:"token-input-dropdown-item",dropdownItem2:"token-input-dropdown-item2",selectedDropdownItem:"token-input-selected-dropdown-item",inputToken:"token-input-input-token"},P=0,O=1,A=2,z=8,_=13,q=27,B=37,E=38,V=39,W=40,G=108,H=188,t={init:function(e,t){var n=I.extend({},o,t||{});return this.each(function(){I(this).data("tokenInputObject",new I.TokenList(this,e,n))})},clear:function(){return this.data("tokenInputObject").clear(),this},add:function(e){return this.data("tokenInputObject").add(e),this},remove:function(e){return this.data("tokenInputObject").remove(e),this},get:function(){return this.data("tokenInputObject").getTokens()}};I.fn.tokenInput=function(e){return t[e]?t[e].apply(this,Array.prototype.slice.call(arguments,1)):t.init.apply(this,arguments)},I.TokenList=function(e,t,a){var n;"string"===I.type(t)||"function"===I.type(t)?(a.url=t,n=S(),void 0===a.crossDomain&&(-1===n.indexOf("://")?a.crossDomain=!1:a.crossDomain=location.href.split(/\/+/g)[1]!==n.split(/\/+/g)[1])):"object"==typeof t&&(a.local_data=t),a.classes?a.classes=I.extend({},F,a.classes):a.theme?(a.classes={},I.each(F,function(e,t){a.classes[e]=t+"-"+a.theme})):a.classes=F;var o,i=[],s=0,l=new I.TokenList.Cache,r=I('<input type="text"  autocomplete="off">').css({outline:"none"}).attr("id",a.idPrefix+e.id).focus(function(){null!==a.tokenLimit&&a.tokenLimit===s||a.hintText&&(k.html("<p>"+a.hintText+"</p>"),D())}).blur(function(){x(),I(this).val("")}).keydown(function(e){var t;switch(e.keyCode){case B:case V:case E:case W:if(I(this).val()){var n=null;return(n=e.keyCode===W||e.keyCode===V?I(p).next():I(p).prev()).length&&j(n),!1}t=f.prev(),n=f.next(),t.length&&t.get(0)===u||n.length&&n.get(0)===u?e.keyCode===B||e.keyCode===E?y(I(u),P):y(I(u),O):e.keyCode!==B&&e.keyCode!==E||!t.length?e.keyCode!==V&&e.keyCode!==W||!n.length||T(I(n.get(0))):T(I(t.get(0)));break;case z:if(t=f.prev(),!I(this).val().length)return u?(C(I(u)),c.change()):t.length&&T(I(t.get(0))),!1;1===I(this).val().length?x():setTimeout(function(){R()},5);break;case _:case G:case H:return p?(v(I(p).data("tokeninput")),c.change()):v(null),!1;case q:return x(),!0;default:String.fromCharCode(e.which)&&setTimeout(function(){R()},5)}}),c=I(e).hide().val("").focus(function(){r.focus()}).blur(function(){r.blur()}),u=null,d=0,p=null,h=I("<ul />").addClass(a.classes.tokenList).click(function(e){e=I(e.target).closest("li");e&&e.get(0)&&I.data(e.get(0),"tokeninput")?function(e){var t=u;u&&y(I(u),A);t===e.get(0)?y(e,A):T(e)}(e):(u&&y(I(u),A),r.focus())}).mouseover(function(e){e=I(e.target).closest("li");e&&u!==this&&e.addClass(a.classes.highlightedToken)}).mouseout(function(e){e=I(e.target).closest("li");e&&u!==this&&e.removeClass(a.classes.highlightedToken)}).insertBefore(c),f=I("<li />").addClass(a.classes.inputToken).appendTo(h).append(r),k=I("<div>").addClass(a.classes.dropdown).appendTo("body").hide();I("<tester/>").insertAfter(r).css({position:"absolute",top:-9999,left:-9999,width:"auto",fontSize:r.css("fontSize"),fontFamily:r.css("fontFamily"),fontWeight:r.css("fontWeight"),letterSpacing:r.css("letterSpacing"),whiteSpace:"nowrap"});c.val("");e=a.prePopulate||c.data("pre");function g(){null!==a.tokenLimit&&s>=a.tokenLimit&&(r.hide(),x())}function m(e){var t=a.tokenFormatter(e),t=I(t).addClass(a.classes.token).insertBefore(f);I("<span>"+a.deleteText+"</span>").addClass(a.classes.tokenDelete).appendTo(t).click(function(){return C(I(this).parent()),c.change(),!1});var n={id:e.id};return n[a.propertyToSearch]=e[a.propertyToSearch],I.data(t.get(0),"tokeninput",e),i=i.slice(0,d).concat([n]).concat(i.slice(d)),d++,w(i,c),s+=1,null!==a.tokenLimit&&s>=a.tokenLimit&&(r.hide(),x()),t}function v(n){var e=a.onAdd;if(!n&&0<r.val().length&&((n={id:r.val()})[a.propertyToSearch]=r.val()),n){if(0<s&&a.preventDuplicates){var o=null;if(h.children().each(function(){var e=I(this),t=I.data(e.get(0),"tokeninput");if(t&&t.id===n.id)return o=e,!1}),o)return T(o),f.insertAfter(o),void r.focus()}(null==a.tokenLimit||s<a.tokenLimit)&&(m(n),g()),r.val(""),x(),I.isFunction(e)&&e.call(c,n)}}function T(e){e.addClass(a.classes.selectedToken),u=e.get(0),r.val(""),x()}function y(e,t){e.removeClass(a.classes.selectedToken),u=null,t===P?(f.insertBefore(e),d--):t===O?(f.insertAfter(e),d++):(f.appendTo(h),d=s),r.focus()}function C(e){var t=I.data(e.get(0),"tokeninput"),n=a.onDelete,o=e.prevAll().length;d<o&&o--,e.remove(),u=null,r.focus(),i=i.slice(0,o).concat(i.slice(o+1)),o<d&&d--,w(i,c),--s,null!==a.tokenLimit&&r.show().val("").focus(),I.isFunction(n)&&n.call(c,t)}function w(e,t){e=I.map(e,function(e){return e[a.tokenValue]});t.val(e.join(a.tokenDelimiter))}function x(){k.hide().empty(),p=null}function D(){k.css({position:"absolute",top:I(h).offset().top+I(h).outerHeight(),left:I(h).offset().left,zindex:999}).show()}function L(e,t,n){return e.replace(new RegExp("(?![^&;]+;)(?!<[^<>]*)("+t+")(?![^<>]*>)(?![^&;]+;)","g"),t.replace(new RegExp("(?![^&;]+;)(?!<[^<>]*)("+n+")(?![^<>]*>)(?![^&;]+;)","gi"),"<b>$1</b>"))}function b(o,e){var i;e&&e.length?(k.empty(),i=I("<ul>").appendTo(k).mouseover(function(e){j(I(e.target).closest("li"))}).mousedown(function(e){return v(I(e.target).closest("li").data("tokeninput")),c.change(),!1}).hide(),I.each(e,function(e,t){var n=L(n=a.resultsFormatter(t),t[a.propertyToSearch],o);n=I(n).appendTo(i),e%2?n.addClass(a.classes.dropdownItem):n.addClass(a.classes.dropdownItem2),0===e&&j(n),I.data(n.get(0),"tokeninput",t)}),D(),a.animateDropdown?i.slideDown("fast"):i.show()):a.noResultsText&&(k.html("<p>"+a.noResultsText+"</p>"),D())}function j(e){e&&(p&&(I(p).removeClass(a.classes.selectedDropdownItem),p=null),e.addClass(a.classes.selectedDropdownItem),p=e.get(0))}function R(){var e=r.val(),t=e.toLowerCase();t&&t.length&&(u&&y(I(u),O),t.length>=a.minChars?(a.searchingText&&(k.html("<p>"+a.searchingText+"</p>"),D()),clearTimeout(o),o=setTimeout(function(){!function(t,n){var o=n+S(),e=l.get(o);{var i,s;e?b(t,e):a.url?(s=S(),i={data:{}},-1<s.indexOf("?")?(e=s.split("?"),i.url=e[0],e=e[1].split("&"),I.each(e,function(e,t){t=t.split("=");i.data[t[0]]=t[1]})):i.url=s,i.data[a.queryParam]=t,i.type=a.method,i.dataType=a.contentType,a.crossDomain&&(i.dataType="jsonp"),i.success=function(e){I.isFunction(a.onResult)&&(e=a.onResult.call(c,e,t,n)),l.add(o,a.jsonContainer?e[a.jsonContainer]:e),r.val().toLowerCase()===t&&b(t,a.jsonContainer?e[a.jsonContainer]:e)},I.ajax(i)):a.local_data&&(s=I.grep(a.local_data,function(e){return-1<e[a.propertyToSearch].toLowerCase().indexOf(t.toLowerCase())}),I.isFunction(a.onResult)&&(s=a.onResult.call(c,s,t,n)),l.add(o,s),b(t,s))}}(t,e)},a.searchDelay)):x())}function S(){var e=a.url;return e="function"==typeof a.url?a.url.call():e}(e=a.processPrePopulate&&I.isFunction(a.onResult)?a.onResult.call(c,e):e)&&e.length&&I.each(e,function(e,t){m(t),g()}),I.isFunction(a.onReady)&&a.onReady.call(),this.clear=function(){h.children("li").each(function(){0===I(this).children("input").length&&C(I(this))})},this.add=function(e){v(e)},this.remove=function(o){h.children("li").each(function(){if(0===I(this).children("input").length){var e,t=I(this).data("tokeninput"),n=!0;for(e in o)if(o[e]!==t[e]){n=!1;break}n&&C(I(this))}})},this.getTokens=function(){return i}},I.TokenList.Cache=function(e){var n=I.extend({max_size:500},e),o={},i=0;this.add=function(e,t){i>n.max_size&&(o={},i=0),o[e]||(i+=1),o[e]=t},this.get=function(e){return o[e]}}}(jQuery);