services:
  nginx:
    image: nginx:stable
    container_name: nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - /root/docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - /root/docker/nginx/conf.d:/etc/nginx/conf.d
      - /root/docker/nginx/certs:/etc/nginx/certs
      - /root/docker/www:/usr/share/nginx/html
    depends_on:
      - php
    restart: always
    environment:
      - TZ=Asia/Hong_Kong
    networks:
      rushb-network:
        ipv4_address: **********

  php:
    build:
      context: ./php
      dockerfile: Dockerfile
    container_name: php
    volumes:
      - /root/docker/www:/var/www/html
    depends_on:
      - mysql
    restart: always
    environment:
      - TZ=Asia/Hong_Kong
    networks:
      rushb-network:
        ipv4_address: **********

  mysql:
    image: mysql:8.4
    container_name: mysql
    volumes:
      - /root/docker/mysql/data:/var/lib/mysql
      - /root/docker/mysql/log:/var/log/mysql
      - /root/docker/mysql/conf:/etc/mysql/conf.d
    environment:
      - MYSQL_ROOT_PASSWORD=qmQcj3jV5sDpDBY8nMuF
      - TZ=Asia/Hong_Kong
    restart: always
    networks:
      rushb-network:
        ipv4_address: **********
      
  subconverter:
    image: tindy2013/subconverter
    container_name: subconverter
    volumes:
      - /root/docker/subconver/pref.toml:/base/pref.toml
    restart: always
    logging:
      driver: "none"
    networks:
      rushb-network:
        ipv4_address: **********

  rsshub:
      # two ways to enable puppeteer:
      # * comment out marked lines, then use this image instead: diygod/rsshub:chromium-bundled
      # * (consumes more disk space and memory) leave everything unchanged
      image: diygod/rsshub
      container_name: rsshub
      restart: always
      environment:
          ACCESS_KEY: "5r6MYnsoh6WUCsX7VwzV"
          NODE_ENV: production
          CACHE_TYPE: redis
          REDIS_URL: "redis://redis:6379/"
          PUPPETEER_WS_ENDPOINT: "ws://browserless:3000" # marked
          DEBUG_INFO: "false"
          FOLLOW_OWNER_USER_ID: "Fanx"      # User id or handle of your follow account
          FOLLOW_DESCRIPTION: "Fanx's instance" # The description of your instance
          FOLLOW_PRICE: 0                 # The monthly price of your instance, set to 0 means free.
          FOLLOW_USER_LIMIT: 1           # The user limit of your instance, set it to 0 or 1 can make your instance private, leaving it empty means no restriction
      healthcheck:
          test: ["CMD", "curl", "-f", "http://localhost:1200/healthz?key=5r6MYnsoh6WUCsX7VwzV"]
          interval: 30s
          timeout: 10s
          retries: 3
      depends_on:
          - redis
          - browserless # marked
      networks:
        rushb-network:
          ipv4_address: **********

  browserless: # marked
      image: browserless/chrome # marked
      container_name: browserless
      restart: always # marked
      ulimits: # marked
          core: # marked
              hard: 0 # marked
              soft: 0 # marked
      healthcheck:
          test: ["CMD", "curl", "-f", "http://localhost:3000/pressure"]
          interval: 30s
          timeout: 10s
          retries: 3
      networks:
        rushb-network:
          ipv4_address: **********
  
  redis:
      image: redis:alpine
      container_name: redis
      restart: always
      volumes:
          - /root/docker/redis-data:/data
      healthcheck:
          test: ["CMD", "redis-cli", "ping"]
          interval: 30s
          timeout: 10s
          retries: 5
          start_period: 5s
      networks:
        rushb-network:
          ipv4_address: **********

  uptime-kuma:
    image: louislam/uptime-kuma
    container_name: uptime-kuma
    restart: always
    volumes:
      - /root/docker/uptime-kuma:/app/data
    networks:
      rushb-network:
        ipv4_address: **********

networks:
  rushb-network:
    ipam:
      config:
        - subnet: **********/16