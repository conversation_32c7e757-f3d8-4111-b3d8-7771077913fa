
user  nginx;
worker_processes  auto;

error_log  /var/log/nginx/error.log notice;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format main '$http_cf_connecting_ip - $remote_user [$time_local] "$host" "$request" '
                '$status $body_bytes_sent "$http_referer" '
                '"$http_user_agent" "$remote_addr"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  30;
    client_body_buffer_size 128k;

    gzip  on;

    ssl_stapling on;
    ssl_stapling_verify on;
    ssl_trusted_certificate /etc/nginx/certs/origin_ca_rsa_root.pem;
    
    # real ip
    set_real_ip_from ************/22;
    set_real_ip_from ************/22;
    set_real_ip_from **********/22;
    set_real_ip_from **********/12;
    set_real_ip_from *************/18;
    set_real_ip_from **********/22;
    set_real_ip_from ************/18;
    set_real_ip_from ***********/15;
    set_real_ip_from **********/13;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from *************/22;
    set_real_ip_from ************/17;
    set_real_ip_from 2400:cb00::/32;
    set_real_ip_from 2606:4700::/32;
    set_real_ip_from 2803:f800::/32;
    set_real_ip_from 2405:b500::/32;
    set_real_ip_from 2405:8100::/32;
    set_real_ip_from 2c0f:f248::/32;
    set_real_ip_from 2a06:98c0::/29;

    real_ip_header CF-Connecting-IP; 
    
    include /etc/nginx/conf.d/*.conf;
}
