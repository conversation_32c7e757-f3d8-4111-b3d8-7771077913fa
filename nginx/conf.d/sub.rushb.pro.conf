server {
    listen 80;
    listen 443 ssl;
    server_name sub.rushb.pro;

    ssl_certificate /etc/nginx/certs/rushb.pro.pem;
    ssl_certificate_key /etc/nginx/certs/rushb.pro.key;
    
    root /usr/share/nginx/html/sub.rushb.pro;
    index index.html;

    if ($server_port !~ 443){
        rewrite ^(/.*)$ https://$host$1 permanent;
    }

    access_log off;
    error_log /dev/null crit;
    location /sub {
        proxy_pass http://subconverter:25500/sub;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    
    location /getruleset {
        proxy_pass http://subconverter:25500/getruleset;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # Static file caching and access control
    location ~ \.(jpg|jpeg|gif|png|js|css)$ {
        root /usr/share/nginx/html/sub.rushb.pro;
        expires 30d;
        access_log off;
        valid_referers sub.rushb.pro;
        if ($invalid_referer) {
           return 404;
        }
    }

    # Error pages
    error_page 403 404 500 502 503 504 /404.html;
    location = /404.html {
        internal;
    }
}
