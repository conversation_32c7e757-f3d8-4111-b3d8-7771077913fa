server {
    listen 80;
    listen 443 ssl;
    server_name oaipro.rushb.pro;

    ssl_certificate /etc/nginx/certs/rushb.pro.pem;
    ssl_certificate_key /etc/nginx/certs/rushb.pro.key;
    
    root /usr/share/nginx/html/default;
    index 404.html;
    client_max_body_size 100M;

    if ($server_port !~ 443){
        rewrite ^(/.*)$ https://$host$1 permanent;
    }

    location = / {
        return 404;
    }

    location = /robots.txt {
        alias /usr/share/nginx/html/default/robots.txt;
    }

    location /v1 {
        proxy_pass https://api.oaipro.com;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_ssl_server_name on;
        proxy_buffering off;
        proxy_intercept_errors on;
        error_page 404 /404.html;
    }

    # Error pages
    error_page 403 404 500 502 503 504 /404.html;
    location = /404.html {
        internal;
    }

}
