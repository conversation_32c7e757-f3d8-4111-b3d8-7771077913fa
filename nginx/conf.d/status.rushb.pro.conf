server {
    listen 80;
    listen 443 ssl;
    server_name status.rushb.pro;

    ssl_certificate /etc/nginx/certs/rushb.pro.pem;
    ssl_certificate_key /etc/nginx/certs/rushb.pro.key;
    
    root /usr/share/nginx/html/default;
    index 404.html;

    if ($server_port !~ 443){
        rewrite ^(/.*)$ https://$host$1 permanent;
    }

  location / {
    proxy_set_header   X-Real-IP $remote_addr;
    proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header   Host $host;
    proxy_pass         http://uptime-kuma:3001/;
    proxy_http_version 1.1;
    proxy_set_header   Upgrade $http_upgrade;
    proxy_set_header   Connection "upgrade";
  }

    location = /robots.txt {
        alias /usr/share/nginx/html/default/robots.txt;
    }

    # Error pages
    error_page 403 404 500 502 503 504 /404.html;
    location = /404.html {
        internal;
    }

}
