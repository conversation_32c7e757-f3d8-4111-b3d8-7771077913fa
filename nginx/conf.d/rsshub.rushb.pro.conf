server {
    listen 80;
    listen 443 ssl;
    server_name rsshub.rushb.pro;

    ssl_certificate /etc/nginx/certs/rushb.pro.pem;
    ssl_certificate_key /etc/nginx/certs/rushb.pro.key;
    
    root /usr/share/nginx/html/default;
    index 404.html;

    if ($server_port !~ 443){
        rewrite ^(/.*)$ https://$host$1 permanent;
    }

    location / {
        proxy_pass http://rsshub:1200/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    location = /robots.txt {
        alias /usr/share/nginx/html/default/robots.txt;
    }

    # Error pages
    error_page 403 404 500 502 503 504 /404.html;
    location = /404.html {
        internal;
    }

}
