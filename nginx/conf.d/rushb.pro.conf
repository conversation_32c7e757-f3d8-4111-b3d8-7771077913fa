server {
    listen 80;
    listen 443 ssl;
    server_name rushb.pro www.rushb.pro;

    # SSL certificate configuration
    ssl_certificate /etc/nginx/certs/rushb.pro.pem;
    ssl_certificate_key /etc/nginx/certs/rushb.pro.key;

    # Redirect HTTP requests to HTTPS
    if ($scheme != "https") {
        return 301 https://$server_name$request_uri;
    }

    # Redirect www.rushb.pro to rushb.pro
    if ($host = 'www.rushb.pro') {
        return 301 https://rushb.pro$request_uri;
    }

    # Document root and index file
    root /usr/share/nginx/html/rushb.pro;
    index index.php index.html;

    # PHP location block
    location ~ \.php$ {
        fastcgi_pass php:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME /var/www/html/rushb.pro$fastcgi_script_name;
        include fastcgi_params;
    }

    # Static file caching and access control
    location ~ \.(jpg|jpeg|gif|png|js|css)$ {
        expires 30d;
        access_log off;
        valid_referers www.rushb.pro rushb.pro;
        if ($invalid_referer) {
           return 404;
        }
    }

    location ~* \.(?:svgz?|jpg|jpeg|gif|png|css|ttf|ttc|otf|eot|woff2?)$ {
        add_header Access-Control-Allow-Origin "*";
        expires    7d;
    }

    # Default location block
    location / {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    # Error pages
    error_page 403 404 500 502 503 504 /404.html;
    location = /404.html {
        internal;
    }
}
