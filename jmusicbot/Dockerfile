FROM openjdk:11-jre-slim

ENV APP_DIR /jmusicbot
WORKDIR $APP_DIR

# 安装 curl
RUN apt-get update && apt-get install -y curl && apt-get clean && rm -rf /var/lib/apt/lists/*

# 下载最新版 JMusicBot
RUN curl -L $(curl -s https://api.github.com/repos/jagrosh/MusicBot/releases/latest \
    | grep browser_download_url | grep JMusicBot | cut -d '"' -f 4) \
    -o $APP_DIR/JMusicBot.jar

COPY config.txt $APP_DIR/config.txt

# 启动 JMusicBot 进程
CMD ["java", "-Dnogui=true", "-jar", "JMusicBot.jar"]
